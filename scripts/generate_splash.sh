#!/bin/bash

# Custom Splash Screen Generator Script
# Usage: ./scripts/generate_splash.sh [logo_path] [android_size] [ios_size] [background_color]

set -e

# Default values
DEFAULT_LOGO="assets/images/ATlogo-white.png"
DEFAULT_ANDROID_SIZE=180
DEFAULT_IOS_SIZE=120
DEFAULT_BACKGROUND_COLOR="#FFB522"

# Parse arguments
LOGO_PATH=${1:-$DEFAULT_LOGO}
ANDROID_SIZE=${2:-$DEFAULT_ANDROID_SIZE}
IOS_SIZE=${3:-$DEFAULT_IOS_SIZE}
BACKGROUND_COLOR=${4:-$DEFAULT_BACKGROUND_COLOR}

echo "🚀 Custom Splash Screen Generator"
echo "=================================="
echo "Logo: $LOGO_PATH"
echo "Android size: ${ANDROID_SIZE}px"
echo "iOS size: ${IOS_SIZE}px"
echo "Background: $BACKGROUND_COLOR"
echo ""

# Check if logo file exists
if [ ! -f "$LOGO_PATH" ]; then
    echo "❌ Error: Logo file '$LOGO_PATH' not found"
    echo ""
    echo "Usage: $0 [logo_path] [android_size] [ios_size] [background_color]"
    echo "Example: $0 assets/images/my-logo.png 180 120 '#FFB522'"
    exit 1
fi

# Check if Python 3 is available
if ! command -v python3 &> /dev/null; then
    echo "❌ Error: Python 3 is required but not installed"
    exit 1
fi

# Check if PIL (Pillow) is available
if ! python3 -c "import PIL" &> /dev/null; then
    echo "❌ Error: Pillow (PIL) is required but not installed"
    echo "Install it with: pip3 install Pillow"
    exit 1
fi

# Create scripts directory if it doesn't exist
mkdir -p scripts

# Run the Python script
python3 scripts/generate_splash.py "$LOGO_PATH" \
    --android-size "$ANDROID_SIZE" \
    --ios-size "$IOS_SIZE" \
    --background-color "$BACKGROUND_COLOR"

echo ""
echo "🎉 Splash screen generation completed!"
echo ""
echo "Next steps:"
echo "1. Run: flutter clean && flutter pub get"
echo "2. Test on your devices"
echo ""
echo "To regenerate with different settings:"
echo "$0 $LOGO_PATH [android_size] [ios_size] [background_color]"

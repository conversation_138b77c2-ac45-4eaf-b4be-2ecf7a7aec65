#!/bin/bash

# Clean up old splash screen files
# This script removes all generated splash screen files

set -e

echo "🧹 Cleaning up old splash screen files..."
echo "========================================"

# Remove Android splash files
echo "Cleaning Android splash files..."
find android/app/src/main/res -name "splash.png" -type f -delete 2>/dev/null || true
find android/app/src/main/res -name "android12splash.png" -type f -delete 2>/dev/null || true

# Remove iOS splash files
echo "Cleaning iOS splash files..."
rm -rf ios/Runner/Assets.xcassets/LaunchImage.imageset 2>/dev/null || true

# Remove flutter_native_splash generated files (if any)
echo "Cleaning flutter_native_splash files..."
rm -f android/app/src/main/res/drawable/background.png 2>/dev/null || true
rm -f ios/Runner/Assets.xcassets/LaunchBackground.imageset/* 2>/dev/null || true

echo ""
echo "✅ Cleanup completed!"
echo ""
echo "You can now run the generate_splash.sh script to create new splash screens."

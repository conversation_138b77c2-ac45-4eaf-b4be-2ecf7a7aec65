#!/usr/bin/env python3
"""
Custom Splash Screen Generator for Flutter Android & iOS
Generates native splash screens without using flutter_native_splash package
"""

import os
import sys
import json
import argparse
from PIL import Image, ImageDraw
import xml.etree.ElementTree as ET
from pathlib import Path

class SplashGenerator:
    def __init__(self, config_file="scripts/splash_config.json"):
        self.config = self.load_config(config_file)
        self.project_root = Path.cwd()
        
    def load_config(self, config_file):
        """Load configuration from JSON file"""
        try:
            with open(config_file, 'r') as f:
                return json.load(f)
        except FileNotFoundError:
            print(f"Config file {config_file} not found. Using default config.")
            return self.get_default_config()
    
    def get_default_config(self):
        """Default configuration"""
        return {
            "background_color": "#FFB522",
            "android": {
                "base_size": 180,
                "densities": {
                    "mdpi": 1.0,
                    "hdpi": 1.5,
                    "xhdpi": 2.0,
                    "xxhdpi": 3.0,
                    "xxxhdpi": 4.0
                },
                "android12_size": 960
            },
            "ios": {
                "size": 120
            }
        }
    
    def hex_to_rgb(self, hex_color):
        """Convert hex color to RGB tuple"""
        hex_color = hex_color.lstrip('#')
        return tuple(int(hex_color[i:i+2], 16) for i in (0, 2, 4))
    
    def create_background_image(self, size, color):
        """Create a solid color background image"""
        rgb_color = self.hex_to_rgb(color)
        img = Image.new('RGBA', (size, size), rgb_color + (255,))
        return img
    
    def resize_logo_with_background(self, logo_path, target_size, background_color):
        """Resize logo and place it on colored background"""
        # Open and resize logo
        logo = Image.open(logo_path).convert('RGBA')
        
        # Calculate logo size (80% of target size to leave some padding)
        logo_size = int(target_size * 0.8)
        logo = logo.resize((logo_size, logo_size), Image.Resampling.LANCZOS)
        
        # Create background
        background = self.create_background_image(target_size, background_color)
        
        # Center logo on background
        x = (target_size - logo_size) // 2
        y = (target_size - logo_size) // 2
        background.paste(logo, (x, y), logo)
        
        return background
    
    def generate_android_splash_images(self, logo_path):
        """Generate Android splash images for all densities"""
        android_config = self.config["android"]
        base_size = android_config["base_size"]
        background_color = self.config["background_color"]
        
        # Create Android drawable directories
        android_res = self.project_root / "android" / "app" / "src" / "main" / "res"
        
        for density, multiplier in android_config["densities"].items():
            # Calculate size for this density (base_size is for xxxhdpi, so divide by 4 first)
            size = int(base_size * multiplier / 4.0)
            
            # Create directory
            drawable_dir = android_res / f"drawable-{density}"
            drawable_dir.mkdir(parents=True, exist_ok=True)
            
            # Generate splash image
            splash_img = self.resize_logo_with_background(logo_path, size, background_color)
            splash_path = drawable_dir / "splash.png"
            splash_img.save(splash_path, "PNG")
            print(f"Generated {splash_path} ({size}x{size})")
        
        # Generate Android 12+ splash (xxxhdpi only)
        android12_size = android_config["android12_size"]
        android12_img = self.resize_logo_with_background(logo_path, android12_size, background_color)
        
        for density in android_config["densities"].keys():
            drawable_dir = android_res / f"drawable-{density}"
            drawable_dir.mkdir(parents=True, exist_ok=True)
            android12_path = drawable_dir / "android12splash.png"
            android12_img.save(android12_path, "PNG")
            print(f"Generated {android12_path} ({android12_size}x{android12_size})")
    
    def generate_ios_splash_images(self, logo_path):
        """Generate iOS splash images"""
        ios_config = self.config["ios"]
        size = ios_config["size"]
        background_color = self.config["background_color"]

        # Create iOS images directory
        ios_images = self.project_root / "ios" / "Runner" / "Assets.xcassets" / "LaunchImage.imageset"
        ios_images.mkdir(parents=True, exist_ok=True)

        # Generate splash image
        splash_img = self.resize_logo_with_background(logo_path, size, background_color)
        splash_path = ios_images / "LaunchImage.png"
        splash_img.save(splash_path, "PNG")
        print(f"Generated {splash_path} ({size}x{size})")

        # Generate 2x and 3x versions
        splash_2x = self.resize_logo_with_background(logo_path, size * 2, background_color)
        splash_2x_path = ios_images / "<EMAIL>"
        splash_2x.save(splash_2x_path, "PNG")
        print(f"Generated {splash_2x_path} ({size*2}x{size*2})")

        splash_3x = self.resize_logo_with_background(logo_path, size * 3, background_color)
        splash_3x_path = ios_images / "<EMAIL>"
        splash_3x.save(splash_3x_path, "PNG")
        print(f"Generated {splash_3x_path} ({size*3}x{size*3})")

        # Create Contents.json for iOS
        self.create_ios_contents_json(ios_images)

    def create_ios_contents_json(self, ios_images_dir):
        """Create Contents.json file for iOS LaunchImage"""
        contents = {
            "images": [
                {
                    "filename": "LaunchImage.png",
                    "idiom": "universal",
                    "scale": "1x"
                },
                {
                    "filename": "<EMAIL>",
                    "idiom": "universal",
                    "scale": "2x"
                },
                {
                    "filename": "<EMAIL>",
                    "idiom": "universal",
                    "scale": "3x"
                }
            ],
            "info": {
                "author": "xcode",
                "version": 1
            }
        }

        contents_path = ios_images_dir / "Contents.json"
        with open(contents_path, 'w') as f:
            json.dump(contents, f, indent=2)
        print(f"Generated {contents_path}")
    
    def update_android_xml_files(self):
        """Update Android XML configuration files"""
        background_color = self.config["background_color"]
        
        # Update launch_background.xml files
        launch_bg_files = [
            "android/app/src/main/res/drawable/launch_background.xml",
            "android/app/src/main/res/drawable-v21/launch_background.xml"
        ]
        
        for file_path in launch_bg_files:
            full_path = self.project_root / file_path
            if full_path.exists():
                self.update_launch_background_xml(full_path, background_color)
        
        # Update styles.xml files
        styles_files = [
            "android/app/src/main/res/values/styles.xml",
            "android/app/src/main/res/values-night/styles.xml",
            "android/app/src/main/res/values-v31/styles.xml",
            "android/app/src/main/res/values-night-v31/styles.xml"
        ]
        
        for file_path in styles_files:
            full_path = self.project_root / file_path
            if full_path.exists():
                self.update_styles_xml(full_path, background_color)
    
    def update_launch_background_xml(self, file_path, background_color):
        """Update launch_background.xml with new background color"""
        try:
            # Register namespace
            ET.register_namespace('android', 'http://schemas.android.com/apk/res/android')

            tree = ET.parse(file_path)
            root = tree.getroot()

            # Find and update background color
            for item in root.findall('.//item'):
                shape = item.find('shape')
                if shape is not None:
                    solid = shape.find('solid')
                    if solid is not None:
                        solid.set('{http://schemas.android.com/apk/res/android}color', background_color)

            tree.write(file_path, encoding='utf-8', xml_declaration=True)
            print(f"Updated {file_path}")
        except Exception as e:
            print(f"Error updating {file_path}: {e}")
    
    def update_styles_xml(self, file_path, background_color):
        """Update styles.xml with new splash screen background color"""
        try:
            tree = ET.parse(file_path)
            root = tree.getroot()
            
            # Update Android 12+ splash background color
            for style in root.findall('.//style[@name="LaunchTheme"]'):
                for item in style.findall('item'):
                    if item.get('name') == 'android:windowSplashScreenBackground':
                        item.text = background_color
            
            tree.write(file_path, encoding='utf-8', xml_declaration=True)
            print(f"Updated {file_path}")
        except Exception as e:
            print(f"Error updating {file_path}: {e}")

def main():
    parser = argparse.ArgumentParser(description='Generate custom splash screens for Flutter')
    parser.add_argument('logo_path', help='Path to the source logo image')
    parser.add_argument('--config', default='scripts/splash_config.json', 
                       help='Path to configuration file')
    parser.add_argument('--android-size', type=int, help='Android base size (overrides config)')
    parser.add_argument('--ios-size', type=int, help='iOS size (overrides config)')
    parser.add_argument('--background-color', help='Background color (overrides config)')
    
    args = parser.parse_args()
    
    # Check if logo file exists
    if not os.path.exists(args.logo_path):
        print(f"Error: Logo file '{args.logo_path}' not found")
        sys.exit(1)
    
    # Initialize generator
    generator = SplashGenerator(args.config)
    
    # Override config with command line arguments
    if args.android_size:
        generator.config["android"]["base_size"] = args.android_size
    if args.ios_size:
        generator.config["ios"]["size"] = args.ios_size
    if args.background_color:
        generator.config["background_color"] = args.background_color
    
    print("Generating splash screens...")
    print(f"Logo: {args.logo_path}")
    print(f"Android base size: {generator.config['android']['base_size']}px")
    print(f"iOS size: {generator.config['ios']['size']}px")
    print(f"Background color: {generator.config['background_color']}")
    print()
    
    # Generate splash images
    generator.generate_android_splash_images(args.logo_path)
    generator.generate_ios_splash_images(args.logo_path)
    
    # Update configuration files
    generator.update_android_xml_files()
    
    print("\n✅ Splash screen generation completed!")
    print("Run 'flutter clean && flutter pub get' to apply changes.")

if __name__ == "__main__":
    main()

#!/bin/bash

# Demo script to test different splash screen configurations

set -e

echo "🎨 Splash Screen Demo"
echo "===================="
echo ""

# Check if logo exists
LOGO="assets/images/ATlogo-white-ios-120.png"
if [ ! -f "$LOGO" ]; then
    echo "❌ Demo logo not found: $LOGO"
    exit 1
fi

echo "Testing different configurations with logo: $LOGO"
echo ""

# Test 1: Default configuration (180px Android, 120px iOS)
echo "📱 Test 1: Default configuration (180px Android, 120px iOS)"
./scripts/generate_splash.sh "$LOGO" 180 120 "#FFB522"
echo "✅ Generated with 180px Android, 120px iOS"
echo ""

# Test 2: Larger Android splash (240px)
echo "📱 Test 2: Larger Android splash (240px Android, 120px iOS)"
./scripts/generate_splash.sh "$LOGO" 240 120 "#FFB522"
echo "✅ Generated with 240px Android, 120px iOS"
echo ""

# Test 3: Different background color
echo "📱 Test 3: Different background color (180px, blue background)"
./scripts/generate_splash.sh "$LOGO" 180 120 "#2196F3"
echo "✅ Generated with blue background"
echo ""

# Test 4: Smaller sizes
echo "📱 Test 4: Smaller sizes (120px Android, 80px iOS)"
./scripts/generate_splash.sh "$LOGO" 120 80 "#FFB522"
echo "✅ Generated with smaller sizes"
echo ""

echo "🎉 Demo completed!"
echo ""
echo "Check the generated files:"
echo "- Android: android/app/src/main/res/drawable-*/splash.png"
echo "- iOS: ios/Runner/Assets.xcassets/LaunchImage.imageset/"
echo ""
echo "Run 'flutter clean && flutter pub get' to apply changes."

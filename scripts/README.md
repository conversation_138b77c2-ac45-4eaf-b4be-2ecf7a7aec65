# Custom Splash Screen Generator

Tự động tạo native splash screen cho Android và iOS từ một logo gốc, không cần sử dụng flutter_native_splash package.

## Yêu cầu

- Python 3.x
- Pillow (PIL): `pip3 install Pillow`

## Cách sử dụng

### 1. Tạo splash screen mới

```bash
# Sử dụng cấu hình mặc định
./scripts/generate_splash.sh

# Tùy chỉnh logo và kích thước
./scripts/generate_splash.sh assets/images/my-logo.png 180 120 "#FFB522"
```

### 2. Dọn dẹp splash screen cũ

```bash
./scripts/clean_splash.sh
```

### 3. Sử dụng script Python trực tiếp

```bash
python3 scripts/generate_splash.py assets/images/ATlogo-white.png \
    --android-size 180 \
    --ios-size 120 \
    --background-color "#FFB522"
```

## Tham số

| Tham số | Mặc định | <PERSON>ô tả |
|---------|----------|-------|
| `logo_path` | `assets/images/ATlogo-white.png` | Đường dẫn đến logo gốc |
| `android_size` | `180` | Kích thước base cho Android (px) |
| `ios_size` | `120` | Kích thước cho iOS (px) |
| `background_color` | `#FFB522` | Màu nền splash screen |

## Cấu hình

Chỉnh sửa file `scripts/splash_config.json` để thay đổi cấu hình mặc định:

```json
{
  "background_color": "#FFB522",
  "android": {
    "base_size": 180,
    "densities": {
      "mdpi": 1.0,
      "hdpi": 1.5,
      "xhdpi": 2.0,
      "xxhdpi": 3.0,
      "xxxhdpi": 4.0
    },
    "android12_size": 960
  },
  "ios": {
    "size": 120
  }
}
```

## Kết quả

### Android
Script sẽ tạo các file splash cho tất cả density:
- `drawable-mdpi/splash.png` (45x45px)
- `drawable-hdpi/splash.png` (67x67px)
- `drawable-xhdpi/splash.png` (90x90px)
- `drawable-xxhdpi/splash.png` (135x135px)
- `drawable-xxxhdpi/splash.png` (180x180px)
- `drawable-*/android12splash.png` (960x960px cho Android 12+)

### iOS
Script sẽ tạo:
- `ios/Runner/Assets.xcassets/LaunchImage.imageset/LaunchImage.png` (120x120px)
- `ios/Runner/Assets.xcassets/LaunchImage.imageset/<EMAIL>` (240x240px)
- `ios/Runner/Assets.xcassets/LaunchImage.imageset/<EMAIL>` (360x360px)
- `ios/Runner/Assets.xcassets/LaunchImage.imageset/Contents.json`

## Cập nhật cấu hình

Script cũng tự động cập nhật:
- Android XML files (launch_background.xml, styles.xml)
- Màu nền splash screen

## Sau khi chạy script

1. Chạy `flutter clean && flutter pub get`
2. Test trên thiết bị thật
3. Nếu cần điều chỉnh, chạy lại script với tham số khác

## Ví dụ

```bash
# Tạo splash với logo 200px cho Android, 150px cho iOS, nền màu xanh
./scripts/generate_splash.sh assets/images/logo.png 200 150 "#0066CC"

# Chỉ thay đổi màu nền
./scripts/generate_splash.sh assets/images/ATlogo-white.png 180 120 "#FF5722"
```

## Gỡ lỗi

Nếu gặp lỗi:
1. Kiểm tra Python 3 đã cài đặt: `python3 --version`
2. Kiểm tra Pillow đã cài đặt: `python3 -c "import PIL; print('OK')"`
3. Kiểm tra file logo tồn tại và có thể đọc được
4. Chạy `chmod +x scripts/*.sh` để cấp quyền thực thi

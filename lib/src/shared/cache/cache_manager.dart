import 'dart:convert';
import 'dart:developer' as developer;
import 'dart:io';
import 'package:flutter/foundation.dart' show kIsWeb;

import 'package:dio/dio.dart';
import 'package:hive_flutter/hive_flutter.dart';
import 'package:path_provider/path_provider.dart';
import 'package:synchronized/synchronized.dart';

import 'cache_config.dart';
import 'cache_entry.dart';
import 'cache_metrics.dart';

/// A manager for handling API response caching
class CacheManager {
  static const String _cacheBoxName = 'api_cache';
  static const String _configBoxName = 'cache_config';
  static const String _metricsBoxName = 'cache_metrics';

  late Box<CacheEntry> _cacheBox;
  late Box<dynamic> _configBox;
  late Box<dynamic> _metricsBox;

  final Lock _lock = Lock();
  bool _initialized = false;

  CacheConfig _config = const CacheConfig();
  CacheMetrics _metrics = CacheMetrics();

  /// Singleton instance
  static final CacheManager _instance = CacheManager._internal();

  /// Factory constructor to return the singleton instance
  factory CacheManager() => _instance;

  /// Private constructor
  CacheManager._internal();

  /// Initialize the cache manager
  Future<void> init() async {
    if (_initialized) return;

    await _lock.synchronized(() async {
      if (_initialized) return;

      if (kIsWeb) {
        await Hive.initFlutter();
      } else {
        final appDocDir = await getApplicationDocumentsDirectory();
        final cacheDir = Directory('${appDocDir.path}/cache');
        if (!cacheDir.existsSync()) {
          cacheDir.createSync(recursive: true);
        }
        await Hive.initFlutter('${appDocDir.path}/cache');
      }

      if (!Hive.isAdapterRegistered(0)) {
        Hive.registerAdapter(CacheEntryAdapter());
      }

      _cacheBox = await Hive.openBox<CacheEntry>(_cacheBoxName);
      _configBox = await Hive.openBox(_configBoxName);
      _metricsBox = await Hive.openBox(_metricsBoxName);

      _loadConfig();
      _loadMetrics();

      await _cleanExpiredEntries();

      _initialized = true;

      developer.log('Cache manager initialized');
    });
  }

  /// Load the cache configuration from storage
  void _loadConfig() {
    final storedConfig = _configBox.get('config');
    if (storedConfig != null) {
      try {
        final Map<String, dynamic> configMap = json.decode(storedConfig);
        _config = CacheConfig(
          defaultTtl: configMap['defaultTtl'] ?? 3600,
          endpointTtls: Map<String, int>.from(configMap['endpointTtls'] ?? {}),
          enableCaching: configMap['enableCaching'] ?? true,
          excludedEndpoints: List<String>.from(configMap['excludedEndpoints'] ?? []),
          cacheOnErrorEndpoints: List<String>.from(configMap['cacheOnErrorEndpoints'] ?? []),
          maxApiCacheSize: configMap['maxApiCacheSize'] ?? configMap['maxCacheSize'] ?? 50 * 1024 * 1024,
          maxImageCacheSize: configMap['maxImageCacheSize'] ?? 100 * 1024 * 1024,
          maxTotalCacheSize: configMap['maxTotalCacheSize'] ?? 200 * 1024 * 1024,
          serveStaleDataOnError: configMap['serveStaleDataOnError'] ?? true,
          cachePostRequests: configMap['cachePostRequests'] ?? false,
          enableImageCaching: configMap['enableImageCaching'] ?? true,
          defaultImageTtl: configMap['defaultImageTtl'] ?? 7 * 24 * 3600,
          cleanupStrategy: CacheCleanupStrategy.values.firstWhere(
            (e) => e.name == (configMap['cleanupStrategy'] ?? 'lru'),
            orElse: () => CacheCleanupStrategy.lru,
          ),
        );
      } catch (e) {
        developer.log('Error loading cache config: $e');
        _config = const CacheConfig();
      }
    }
  }

  /// Load the cache metrics from storage
  void _loadMetrics() {
    final storedMetrics = _metricsBox.get('metrics');
    if (storedMetrics != null) {
      try {
        final Map<String, dynamic> metricsMap = json.decode(storedMetrics);
        _metrics = CacheMetrics.fromJson(metricsMap);
      } catch (e) {
        developer.log('Error loading cache metrics: $e');
        _metrics = CacheMetrics();
      }
    }
  }

  /// Save the cache configuration to storage
  Future<void> _saveConfig() async {
    await _configBox.put('config', json.encode(_config.toJson()));
  }

  /// Save the cache metrics to storage
  Future<void> _saveMetrics() async {
    await _metricsBox.put('metrics', json.encode(_metrics.toJson()));
  }

  /// Get the current cache configuration
  CacheConfig get config => _config;

  /// Get the current cache metrics
  CacheMetrics get metrics => _metrics;

  /// Update the cache configuration
  Future<void> updateConfig(CacheConfig newConfig) async {
    _config = newConfig;
    await _saveConfig();
  }

  /// Get a cache key for a request
  String _getCacheKey(String url, String method, Map<String, dynamic>? queryParams) {
    if (queryParams != null && queryParams.isNotEmpty) {
      final sortedParams = Map.fromEntries(queryParams.entries.toList()..sort((a, b) => a.key.compareTo(b.key)));
      return '$method:$url:${json.encode(sortedParams)}';
    }
    return '$method:$url';
  }

  /// Get a cached response for a request
  Future<CacheEntry?> getCachedResponse(String url, String method, Map<String, dynamic>? queryParams) async {
    await init();

    final cacheKey = _getCacheKey(url, method, queryParams);
    final cachedEntry = _cacheBox.get(cacheKey);

    if (cachedEntry != null) {
      if (cachedEntry.isValid) {
        _metrics.incrementHits();
        await _saveMetrics();
        return cachedEntry;
      } else {
        _metrics.incrementExpired();
        await _saveMetrics();
        return null;
      }
    } else {
      _metrics.incrementMisses();
      await _saveMetrics();
      return null;
    }
  }

  /// Cache a response
  Future<void> cacheResponse(
    String url,
    String method,
    Map<String, dynamic>? queryParams,
    Response response,
    int ttl,
  ) async {
    await init();

    final cacheKey = _getCacheKey(url, method, queryParams);

    final headers = <String, dynamic>{};
    response.headers.forEach((name, values) {
      headers[name] = values.join(', ');
    });

    final entry = CacheEntry(
      data: json.encode(response.data),
      headers: headers,
      createdAt: DateTime.now().millisecondsSinceEpoch,
      ttl: ttl,
      statusCode: response.statusCode ?? 200,
      url: url,
      method: method,
    );

    await _cacheBox.put(cacheKey, entry);
    _metrics.incrementWrites();
    await _saveMetrics();

    await _enforceMaxCacheSize();
  }

  /// Clean up expired cache entries
  Future<void> _cleanExpiredEntries() async {
    final now = DateTime.now().millisecondsSinceEpoch;
    final keysToDelete = <String>[];

    for (final key in _cacheBox.keys) {
      final entry = _cacheBox.get(key);
      if (entry != null) {
        final expiresAt = entry.createdAt + (entry.ttl * 1000);
        if (now > expiresAt) {
          keysToDelete.add(key);
        }
      }
    }

    if (keysToDelete.isNotEmpty) {
      await _cacheBox.deleteAll(keysToDelete);
      developer.log('Cleaned ${keysToDelete.length} expired cache entries');
    }
  }

  /// Enforce the maximum cache size
  Future<void> _enforceMaxCacheSize() async {
    int currentSize = 0;
    final entriesByAge = <String, CacheEntry>{};

    for (final key in _cacheBox.keys) {
      final entry = _cacheBox.get(key);
      if (entry != null) {
        currentSize += entry.data.length;
        entriesByAge[key] = entry;
      }
    }

    if (currentSize > _config.maxApiCacheSize) {
      final sortedEntries = entriesByAge.entries.toList()
        ..sort((a, b) => a.value.createdAt.compareTo(b.value.createdAt));

      final keysToDelete = <String>[];
      int sizeToFree = currentSize - _config.maxApiCacheSize;

      for (final entry in sortedEntries) {
        keysToDelete.add(entry.key);
        sizeToFree -= entry.value.data.length;
        if (sizeToFree <= 0) break;
      }

      if (keysToDelete.isNotEmpty) {
        await _cacheBox.deleteAll(keysToDelete);
        developer.log('Deleted ${keysToDelete.length} cache entries to enforce size limit');
      }
    }
  }

  /// Clear the entire cache
  Future<void> clearCache() async {
    await init();
    await _cacheBox.clear();
    _metrics.reset();
    await _saveMetrics();
    developer.log('Cache cleared');
  }

  /// Clear specific endpoints from the cache
  Future<void> clearEndpoint(String endpoint) async {
    await init();

    final keysToDelete = <String>[];

    for (final key in _cacheBox.keys) {
      final entry = _cacheBox.get(key);
      if (entry != null && entry.url.contains(endpoint)) {
        keysToDelete.add(key);
      }
    }

    if (keysToDelete.isNotEmpty) {
      await _cacheBox.deleteAll(keysToDelete);
      developer.log('Cleared ${keysToDelete.length} entries for endpoint: $endpoint');
    }
  }

  /// Clear all cache entries that contain a specific site ID in their URL
  /// This is useful when switching sites to ensure no stale site-specific data remains
  Future<void> clearSiteSpecificCache(int siteId) async {
    await init();

    final keysToDelete = <String>[];
    final sitePattern = '/sites/$siteId/';
    final campaignCountSummaryPattern = '/sites/$siteId/campaigns/count-summary';
    bool campaignCountSummaryCleared = false;

    for (final key in _cacheBox.keys) {
      final entry = _cacheBox.get(key);
      if (entry != null && entry.url.contains(sitePattern)) {
        keysToDelete.add(key);

        if (entry.url.contains(campaignCountSummaryPattern)) {
          campaignCountSummaryCleared = true;
          developer.log('🎯 Campaign count summary cache cleared for site ID: $siteId');
        }
      }
    }

    if (keysToDelete.isNotEmpty) {
      await _cacheBox.deleteAll(keysToDelete);
      developer.log('Cleared ${keysToDelete.length} site-specific cache entries for site ID: $siteId');

      if (campaignCountSummaryCleared) {
        developer.log('✅ Campaign count summary cache successfully cleared for site ID: $siteId');
      }
    } else {
      developer.log('No site-specific cache entries found for site ID: $siteId');
    }
  }

  /// Get the current cache size in bytes
  Future<int> getCacheSize() async {
    await init();

    int size = 0;
    for (final key in _cacheBox.keys) {
      final entry = _cacheBox.get(key);
      if (entry != null) {
        size += entry.data.length;
      }
    }

    return size;
  }

  /// Get the number of entries in the cache
  Future<int> getCacheEntryCount() async {
    await init();
    return _cacheBox.length;
  }

  /// Warm up the cache for specific endpoints
  Future<void> warmCache(List<String> endpoints, Dio dio) async {
    for (final endpoint in endpoints) {
      try {
        await dio.get(endpoint);
        developer.log('Warmed cache for endpoint: $endpoint');
      } catch (e) {
        developer.log('Error warming cache for endpoint: $endpoint - $e');
      }
    }
  }

  /// Get all cache keys with detailed information
  Future<List<Map<String, dynamic>>> getAllCacheKeys() async {
    await init();

    final cacheKeys = <Map<String, dynamic>>[];

    for (final key in _cacheBox.keys) {
      final entry = _cacheBox.get(key);
      if (entry != null) {
        final sizeInBytes = entry.data.length;
        final sizeInKB = (sizeInBytes / 1024).toStringAsFixed(2);

        final expirationDate = entry.expiresAt;
        final formattedExpiration = '${expirationDate.year.toString().padLeft(4, '0')}/'
            '${expirationDate.month.toString().padLeft(2, '0')}/'
            '${expirationDate.day.toString().padLeft(2, '0')} '
            '${expirationDate.hour.toString().padLeft(2, '0')}:'
            '${expirationDate.minute.toString().padLeft(2, '0')}:'
            '${expirationDate.second.toString().padLeft(2, '0')}';

        cacheKeys.add({
          'key': key,
          'url': entry.url,
          'method': entry.method,
          'sizeKB': sizeInKB,
          'sizeBytes': sizeInBytes,
          'ttl': entry.ttl,
          'expirationDate': formattedExpiration,
          'isValid': entry.isValid,
          'timeRemaining': entry.timeRemaining,
          'createdAt': entry.createdAtDate.toIso8601String(),
        });
      }
    }

    cacheKeys.sort((a, b) => b['createdAt'].compareTo(a['createdAt']));

    return cacheKeys;
  }
}

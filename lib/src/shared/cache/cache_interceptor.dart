import 'dart:convert';
import 'dart:developer' as developer;
import 'dart:io';

import 'package:dio/dio.dart';

import 'cache_config.dart';
import 'cache_entry.dart';
import 'cache_manager.dart';

/// An interceptor for Dio that handles caching of API responses
class CacheInterceptor extends Interceptor {
  final CacheManager _cacheManager;
  final CacheConfig _config;

  /// Key for storing whether a request should skip cache
  static const String skipCacheKey = 'skipCache';
  
  /// Key for storing whether a response should be cached
  static const String forceCacheKey = 'forceCache';
  
  /// Key for storing a custom TTL for a request
  static const String cacheTtlKey = 'cacheTtl';

  CacheInterceptor({
    CacheManager? cacheManager,
    CacheConfig? config,
  }) : 
    _cacheManager = cacheManager ?? CacheManager(),
    _config = config ?? const CacheConfig();

  @override
  void onRequest(RequestOptions options, RequestInterceptorHandler handler) async {
    // Initialize cache manager if not already initialized
    await _cacheManager.init();
    
    // Skip cache if requested
    if (options.extra[skipCacheKey] == true) {
      return handler.next(options);
    }
    
    final method = options.method;
    final url = options.path;
    
    // Check if we should cache this endpoint
    if (!_config.shouldCacheEndpoint(url, method)) {
      return handler.next(options);
    }
    
    try {
      // Try to get cached response
      final cachedEntry = await _cacheManager.getCachedResponse(
        url, 
        method, 
        options.queryParameters,
      );
      
      if (cachedEntry != null) {
        // We have a valid cached response
        developer.log('🔵 [CACHE HIT] $method $url (TTL: ${cachedEntry.timeRemaining}s)');
        
        // Create a response from the cached data
        final responseData = cachedEntry.decodedData;
        final headers = Headers.fromMap(
          Map<String, List<String>>.fromEntries(
            cachedEntry.headers.entries.map(
              (e) => MapEntry(e.key, [e.value.toString()])
            )
          )
        );
        
        // Add cache-specific headers
        headers.set('x-cache', 'HIT');
        headers.set('x-cache-ttl', cachedEntry.timeRemaining.toString());
        headers.set('x-cache-expires', cachedEntry.expiresAt.toIso8601String());
        
        final response = Response(
          data: responseData,
          headers: headers,
          requestOptions: options,
          statusCode: cachedEntry.statusCode,
          isRedirect: false,
          statusMessage: 'OK (Cached)',
        );
        
        return handler.resolve(response);
      }
    } catch (e) {
      developer.log('Error retrieving from cache: $e');
      // Continue with the request if there's an error with the cache
    }
    
    // No cache hit, proceed with the request
    return handler.next(options);
  }

  @override
  void onResponse(Response response, ResponseInterceptorHandler handler) async {
    // Skip caching if requested
    if (response.requestOptions.extra[skipCacheKey] == true) {
      return handler.next(response);
    }
    
    final method = response.requestOptions.method;
    final url = response.requestOptions.path;
    
    // Check if we should cache this endpoint
    final shouldCache = _config.shouldCacheEndpoint(url, method) || 
                        response.requestOptions.extra[forceCacheKey] == true;
    
    if (!shouldCache) {
      return handler.next(response);
    }
    
    try {
      // Get TTL for this endpoint
      int ttl = response.requestOptions.extra[cacheTtlKey] as int? ?? 
                _config.getTtlForEndpoint(url);
      
      // Cache the response
      await _cacheManager.cacheResponse(
        url,
        method,
        response.requestOptions.queryParameters,
        response,
        ttl,
      );
      
      developer.log('🟢 [CACHE WRITE] $method $url (TTL: ${ttl}s)');
      
      // Add cache-specific headers
      response.headers.set('x-cache', 'MISS');
      response.headers.set('x-cache-ttl', ttl.toString());
      final expiresAt = DateTime.now().add(Duration(seconds: ttl));
      response.headers.set('x-cache-expires', expiresAt.toIso8601String());
    } catch (e) {
      developer.log('Error caching response: $e');
      // Continue with the response even if caching fails
    }
    
    return handler.next(response);
  }

  @override
  void onError(DioException err, ErrorInterceptorHandler handler) async {
    final method = err.requestOptions.method;
    final url = err.requestOptions.path;
    
    // Check if we should serve stale data on error
    if (!_config.serveStaleDataOnError && 
        !_config.shouldCacheOnError(url)) {
      return handler.next(err);
    }
    
    // Check if the error is a network error
    final isNetworkError = err.type == DioExceptionType.connectionTimeout ||
                          err.type == DioExceptionType.receiveTimeout ||
                          err.type == DioExceptionType.sendTimeout ||
                          err.error is SocketException;
    
    // Check if the error is a server error (5xx)
    final isServerError = err.response?.statusCode != null && 
                         err.response!.statusCode! >= 500;
    
    // Only serve from cache for network errors or server errors
    if (!isNetworkError && !isServerError) {
      return handler.next(err);
    }
    
    try {
      // Try to get cached response, even if expired
      final cachedEntry = await _cacheManager.getCachedResponse(
        url, 
        method, 
        err.requestOptions.queryParameters,
      );
      
      if (cachedEntry != null) {
        // We have a cached response, use it as fallback
        developer.log('🟠 [CACHE FALLBACK] $method $url (Error: ${err.message})');
        
        // Create a response from the cached data
        final responseData = cachedEntry.decodedData;
        final headers = Headers.fromMap(
          Map<String, List<String>>.fromEntries(
            cachedEntry.headers.entries.map(
              (e) => MapEntry(e.key, [e.value.toString()])
            )
          )
        );
        
        // Add cache-specific headers
        headers.set('x-cache', 'FALLBACK');
        headers.set('x-cache-stale', (!cachedEntry.isValid).toString());
        headers.set('x-cache-error', err.message ?? 'Unknown error');
        
        final response = Response(
          data: responseData,
          headers: headers,
          requestOptions: err.requestOptions,
          statusCode: cachedEntry.statusCode,
          isRedirect: false,
          statusMessage: 'OK (Cached Fallback)',
        );
        
        // Update metrics
        _cacheManager.metrics.incrementFallbacks();
        
        return handler.resolve(response);
      }
    } catch (e) {
      developer.log('Error retrieving from cache for fallback: $e');
    }
    
    // No cache hit, proceed with the error
    return handler.next(err);
  }
}

import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:koc_app/src/base/cubit/base_cubit_state.dart';
import 'package:koc_app/src/modules/report/data/model/report_model.dart';
import 'package:koc_app/src/shared/data/item.dart';

part 'filter_state.freezed.dart';
part 'filter_state.g.dart';

@freezed
class FilterState extends BaseCubitState with _$FilterState {
  factory FilterState({
    @Default([]) List<ReportPeriod> periods,
    @Default(ReportPeriod.LAST_12_MONTHS) ReportPeriod selectedPeriod,
    DateTime? startDate,
    DateTime? endDate,
    DateTime? customStartDate,
    DateTime? customEndDate,
    @Default(ReportQueryPeriodBase.CONVERSION_DATE) ReportQueryPeriodBase selectedDateType,
    ConversionStatus? selectedStatus,
    @Default([]) List<Item> campaigns,
    Item? selectedCampaign,
    @Default('') String invoiceId,
    @Default('') String customName,
    @Default('') String errorMessage,
    Item? selectedSite,
  }) = _FilterState;

  factory FilterState.fromJson(Map<String, Object?> json) => _$FilterStateFromJson(json);
}

// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'filter_state.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

FilterState _$FilterStateFromJson(Map<String, dynamic> json) {
  return _FilterState.fromJson(json);
}

/// @nodoc
mixin _$FilterState {
  List<ReportPeriod> get periods => throw _privateConstructorUsedError;
  ReportPeriod get selectedPeriod => throw _privateConstructorUsedError;
  DateTime? get startDate => throw _privateConstructorUsedError;
  DateTime? get endDate => throw _privateConstructorUsedError;
  DateTime? get customStartDate => throw _privateConstructorUsedError;
  DateTime? get customEndDate => throw _privateConstructorUsedError;
  ReportQueryPeriodBase get selectedDateType =>
      throw _privateConstructorUsedError;
  ConversionStatus? get selectedStatus => throw _privateConstructorUsedError;
  List<Item> get campaigns => throw _privateConstructorUsedError;
  Item? get selectedCampaign => throw _privateConstructorUsedError;
  String get invoiceId => throw _privateConstructorUsedError;
  String get customName => throw _privateConstructorUsedError;
  String get errorMessage => throw _privateConstructorUsedError;
  Item? get selectedSite => throw _privateConstructorUsedError;

  /// Serializes this FilterState to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of FilterState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $FilterStateCopyWith<FilterState> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $FilterStateCopyWith<$Res> {
  factory $FilterStateCopyWith(
          FilterState value, $Res Function(FilterState) then) =
      _$FilterStateCopyWithImpl<$Res, FilterState>;
  @useResult
  $Res call(
      {List<ReportPeriod> periods,
      ReportPeriod selectedPeriod,
      DateTime? startDate,
      DateTime? endDate,
      DateTime? customStartDate,
      DateTime? customEndDate,
      ReportQueryPeriodBase selectedDateType,
      ConversionStatus? selectedStatus,
      List<Item> campaigns,
      Item? selectedCampaign,
      String invoiceId,
      String customName,
      String errorMessage,
      Item? selectedSite});

  $ItemCopyWith<$Res>? get selectedCampaign;
  $ItemCopyWith<$Res>? get selectedSite;
}

/// @nodoc
class _$FilterStateCopyWithImpl<$Res, $Val extends FilterState>
    implements $FilterStateCopyWith<$Res> {
  _$FilterStateCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of FilterState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? periods = null,
    Object? selectedPeriod = null,
    Object? startDate = freezed,
    Object? endDate = freezed,
    Object? customStartDate = freezed,
    Object? customEndDate = freezed,
    Object? selectedDateType = null,
    Object? selectedStatus = freezed,
    Object? campaigns = null,
    Object? selectedCampaign = freezed,
    Object? invoiceId = null,
    Object? customName = null,
    Object? errorMessage = null,
    Object? selectedSite = freezed,
  }) {
    return _then(_value.copyWith(
      periods: null == periods
          ? _value.periods
          : periods // ignore: cast_nullable_to_non_nullable
              as List<ReportPeriod>,
      selectedPeriod: null == selectedPeriod
          ? _value.selectedPeriod
          : selectedPeriod // ignore: cast_nullable_to_non_nullable
              as ReportPeriod,
      startDate: freezed == startDate
          ? _value.startDate
          : startDate // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      endDate: freezed == endDate
          ? _value.endDate
          : endDate // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      customStartDate: freezed == customStartDate
          ? _value.customStartDate
          : customStartDate // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      customEndDate: freezed == customEndDate
          ? _value.customEndDate
          : customEndDate // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      selectedDateType: null == selectedDateType
          ? _value.selectedDateType
          : selectedDateType // ignore: cast_nullable_to_non_nullable
              as ReportQueryPeriodBase,
      selectedStatus: freezed == selectedStatus
          ? _value.selectedStatus
          : selectedStatus // ignore: cast_nullable_to_non_nullable
              as ConversionStatus?,
      campaigns: null == campaigns
          ? _value.campaigns
          : campaigns // ignore: cast_nullable_to_non_nullable
              as List<Item>,
      selectedCampaign: freezed == selectedCampaign
          ? _value.selectedCampaign
          : selectedCampaign // ignore: cast_nullable_to_non_nullable
              as Item?,
      invoiceId: null == invoiceId
          ? _value.invoiceId
          : invoiceId // ignore: cast_nullable_to_non_nullable
              as String,
      customName: null == customName
          ? _value.customName
          : customName // ignore: cast_nullable_to_non_nullable
              as String,
      errorMessage: null == errorMessage
          ? _value.errorMessage
          : errorMessage // ignore: cast_nullable_to_non_nullable
              as String,
      selectedSite: freezed == selectedSite
          ? _value.selectedSite
          : selectedSite // ignore: cast_nullable_to_non_nullable
              as Item?,
    ) as $Val);
  }

  /// Create a copy of FilterState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $ItemCopyWith<$Res>? get selectedCampaign {
    if (_value.selectedCampaign == null) {
      return null;
    }

    return $ItemCopyWith<$Res>(_value.selectedCampaign!, (value) {
      return _then(_value.copyWith(selectedCampaign: value) as $Val);
    });
  }

  /// Create a copy of FilterState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $ItemCopyWith<$Res>? get selectedSite {
    if (_value.selectedSite == null) {
      return null;
    }

    return $ItemCopyWith<$Res>(_value.selectedSite!, (value) {
      return _then(_value.copyWith(selectedSite: value) as $Val);
    });
  }
}

/// @nodoc
abstract class _$$FilterStateImplCopyWith<$Res>
    implements $FilterStateCopyWith<$Res> {
  factory _$$FilterStateImplCopyWith(
          _$FilterStateImpl value, $Res Function(_$FilterStateImpl) then) =
      __$$FilterStateImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {List<ReportPeriod> periods,
      ReportPeriod selectedPeriod,
      DateTime? startDate,
      DateTime? endDate,
      DateTime? customStartDate,
      DateTime? customEndDate,
      ReportQueryPeriodBase selectedDateType,
      ConversionStatus? selectedStatus,
      List<Item> campaigns,
      Item? selectedCampaign,
      String invoiceId,
      String customName,
      String errorMessage,
      Item? selectedSite});

  @override
  $ItemCopyWith<$Res>? get selectedCampaign;
  @override
  $ItemCopyWith<$Res>? get selectedSite;
}

/// @nodoc
class __$$FilterStateImplCopyWithImpl<$Res>
    extends _$FilterStateCopyWithImpl<$Res, _$FilterStateImpl>
    implements _$$FilterStateImplCopyWith<$Res> {
  __$$FilterStateImplCopyWithImpl(
      _$FilterStateImpl _value, $Res Function(_$FilterStateImpl) _then)
      : super(_value, _then);

  /// Create a copy of FilterState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? periods = null,
    Object? selectedPeriod = null,
    Object? startDate = freezed,
    Object? endDate = freezed,
    Object? customStartDate = freezed,
    Object? customEndDate = freezed,
    Object? selectedDateType = null,
    Object? selectedStatus = freezed,
    Object? campaigns = null,
    Object? selectedCampaign = freezed,
    Object? invoiceId = null,
    Object? customName = null,
    Object? errorMessage = null,
    Object? selectedSite = freezed,
  }) {
    return _then(_$FilterStateImpl(
      periods: null == periods
          ? _value._periods
          : periods // ignore: cast_nullable_to_non_nullable
              as List<ReportPeriod>,
      selectedPeriod: null == selectedPeriod
          ? _value.selectedPeriod
          : selectedPeriod // ignore: cast_nullable_to_non_nullable
              as ReportPeriod,
      startDate: freezed == startDate
          ? _value.startDate
          : startDate // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      endDate: freezed == endDate
          ? _value.endDate
          : endDate // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      customStartDate: freezed == customStartDate
          ? _value.customStartDate
          : customStartDate // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      customEndDate: freezed == customEndDate
          ? _value.customEndDate
          : customEndDate // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      selectedDateType: null == selectedDateType
          ? _value.selectedDateType
          : selectedDateType // ignore: cast_nullable_to_non_nullable
              as ReportQueryPeriodBase,
      selectedStatus: freezed == selectedStatus
          ? _value.selectedStatus
          : selectedStatus // ignore: cast_nullable_to_non_nullable
              as ConversionStatus?,
      campaigns: null == campaigns
          ? _value._campaigns
          : campaigns // ignore: cast_nullable_to_non_nullable
              as List<Item>,
      selectedCampaign: freezed == selectedCampaign
          ? _value.selectedCampaign
          : selectedCampaign // ignore: cast_nullable_to_non_nullable
              as Item?,
      invoiceId: null == invoiceId
          ? _value.invoiceId
          : invoiceId // ignore: cast_nullable_to_non_nullable
              as String,
      customName: null == customName
          ? _value.customName
          : customName // ignore: cast_nullable_to_non_nullable
              as String,
      errorMessage: null == errorMessage
          ? _value.errorMessage
          : errorMessage // ignore: cast_nullable_to_non_nullable
              as String,
      selectedSite: freezed == selectedSite
          ? _value.selectedSite
          : selectedSite // ignore: cast_nullable_to_non_nullable
              as Item?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$FilterStateImpl implements _FilterState {
  _$FilterStateImpl(
      {final List<ReportPeriod> periods = const [],
      this.selectedPeriod = ReportPeriod.LAST_12_MONTHS,
      this.startDate,
      this.endDate,
      this.customStartDate,
      this.customEndDate,
      this.selectedDateType = ReportQueryPeriodBase.CONVERSION_DATE,
      this.selectedStatus,
      final List<Item> campaigns = const [],
      this.selectedCampaign,
      this.invoiceId = '',
      this.customName = '',
      this.errorMessage = '',
      this.selectedSite})
      : _periods = periods,
        _campaigns = campaigns;

  factory _$FilterStateImpl.fromJson(Map<String, dynamic> json) =>
      _$$FilterStateImplFromJson(json);

  final List<ReportPeriod> _periods;
  @override
  @JsonKey()
  List<ReportPeriod> get periods {
    if (_periods is EqualUnmodifiableListView) return _periods;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_periods);
  }

  @override
  @JsonKey()
  final ReportPeriod selectedPeriod;
  @override
  final DateTime? startDate;
  @override
  final DateTime? endDate;
  @override
  final DateTime? customStartDate;
  @override
  final DateTime? customEndDate;
  @override
  @JsonKey()
  final ReportQueryPeriodBase selectedDateType;
  @override
  final ConversionStatus? selectedStatus;
  final List<Item> _campaigns;
  @override
  @JsonKey()
  List<Item> get campaigns {
    if (_campaigns is EqualUnmodifiableListView) return _campaigns;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_campaigns);
  }

  @override
  final Item? selectedCampaign;
  @override
  @JsonKey()
  final String invoiceId;
  @override
  @JsonKey()
  final String customName;
  @override
  @JsonKey()
  final String errorMessage;
  @override
  final Item? selectedSite;

  @override
  String toString() {
    return 'FilterState(periods: $periods, selectedPeriod: $selectedPeriod, startDate: $startDate, endDate: $endDate, customStartDate: $customStartDate, customEndDate: $customEndDate, selectedDateType: $selectedDateType, selectedStatus: $selectedStatus, campaigns: $campaigns, selectedCampaign: $selectedCampaign, invoiceId: $invoiceId, customName: $customName, errorMessage: $errorMessage, selectedSite: $selectedSite)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$FilterStateImpl &&
            const DeepCollectionEquality().equals(other._periods, _periods) &&
            (identical(other.selectedPeriod, selectedPeriod) ||
                other.selectedPeriod == selectedPeriod) &&
            (identical(other.startDate, startDate) ||
                other.startDate == startDate) &&
            (identical(other.endDate, endDate) || other.endDate == endDate) &&
            (identical(other.customStartDate, customStartDate) ||
                other.customStartDate == customStartDate) &&
            (identical(other.customEndDate, customEndDate) ||
                other.customEndDate == customEndDate) &&
            (identical(other.selectedDateType, selectedDateType) ||
                other.selectedDateType == selectedDateType) &&
            (identical(other.selectedStatus, selectedStatus) ||
                other.selectedStatus == selectedStatus) &&
            const DeepCollectionEquality()
                .equals(other._campaigns, _campaigns) &&
            (identical(other.selectedCampaign, selectedCampaign) ||
                other.selectedCampaign == selectedCampaign) &&
            (identical(other.invoiceId, invoiceId) ||
                other.invoiceId == invoiceId) &&
            (identical(other.customName, customName) ||
                other.customName == customName) &&
            (identical(other.errorMessage, errorMessage) ||
                other.errorMessage == errorMessage) &&
            (identical(other.selectedSite, selectedSite) ||
                other.selectedSite == selectedSite));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      const DeepCollectionEquality().hash(_periods),
      selectedPeriod,
      startDate,
      endDate,
      customStartDate,
      customEndDate,
      selectedDateType,
      selectedStatus,
      const DeepCollectionEquality().hash(_campaigns),
      selectedCampaign,
      invoiceId,
      customName,
      errorMessage,
      selectedSite);

  /// Create a copy of FilterState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$FilterStateImplCopyWith<_$FilterStateImpl> get copyWith =>
      __$$FilterStateImplCopyWithImpl<_$FilterStateImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$FilterStateImplToJson(
      this,
    );
  }
}

abstract class _FilterState implements FilterState {
  factory _FilterState(
      {final List<ReportPeriod> periods,
      final ReportPeriod selectedPeriod,
      final DateTime? startDate,
      final DateTime? endDate,
      final DateTime? customStartDate,
      final DateTime? customEndDate,
      final ReportQueryPeriodBase selectedDateType,
      final ConversionStatus? selectedStatus,
      final List<Item> campaigns,
      final Item? selectedCampaign,
      final String invoiceId,
      final String customName,
      final String errorMessage,
      final Item? selectedSite}) = _$FilterStateImpl;

  factory _FilterState.fromJson(Map<String, dynamic> json) =
      _$FilterStateImpl.fromJson;

  @override
  List<ReportPeriod> get periods;
  @override
  ReportPeriod get selectedPeriod;
  @override
  DateTime? get startDate;
  @override
  DateTime? get endDate;
  @override
  DateTime? get customStartDate;
  @override
  DateTime? get customEndDate;
  @override
  ReportQueryPeriodBase get selectedDateType;
  @override
  ConversionStatus? get selectedStatus;
  @override
  List<Item> get campaigns;
  @override
  Item? get selectedCampaign;
  @override
  String get invoiceId;
  @override
  String get customName;
  @override
  String get errorMessage;
  @override
  Item? get selectedSite;

  /// Create a copy of FilterState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$FilterStateImplCopyWith<_$FilterStateImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

import 'package:dio/dio.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:koc_app/src/base/cubit/base_cubit.dart';
import 'package:koc_app/src/modules/campaign/data/repository/campaign_repository.dart';
import 'package:koc_app/src/modules/report/data/model/report_model.dart';
import 'package:koc_app/src/modules/shared/cubit/filter_state.dart';
import 'package:koc_app/src/modules/shared/mixin/filter_mixin.dart';
import 'package:koc_app/src/shared/constants/date_time_constants.dart';
import 'package:koc_app/src/shared/data/item.dart';
import 'package:koc_app/src/shared/utils/handle_error.dart';

class FilterCubit extends BaseCubit<FilterState> with FilterMixin {
  final CampaignRepository campaignRepository;
  FilterCubit(this.campaignRepository) : super(FilterState());

  void selectPeriod(ReportPeriod period) {
    DateTimeRange range = getTimeRange(period, state.customStartDate, state.customEndDate);
    emit(state.copyWith(selectedPeriod: period, startDate: range.start, endDate: range.end));
  }

  void selectDateType(ReportQueryPeriodBase dateType) {
    emit(state.copyWith(selectedDateType: dateType));
  }

  void selectStatus(ConversionStatus? status) {
    emit(state.copyWith(selectedStatus: status));
  }

  void selectCampaign(Item? campaign) {
    emit(state.copyWith(selectedCampaign: campaign));
  }

  void selectSite(Item? site) {
    emit(state.copyWith(selectedSite: site));
  }

  void selectRangeDate(DateTime? startDate, DateTime? endDate) {
    emit(state.copyWith(startDate: startDate, endDate: endDate));
  }

  void selectCustomRangeDate(DateTime? startDate, DateTime? endDate) {
    emit(state.copyWith(
        startDate: startDate,
        customStartDate: startDate,
        endDate: endDate,
        customEndDate: endDate,
        selectedPeriod: ReportPeriod.CUSTOM_RANGE));
  }

  void selectRewardMonth(String month) {
    DateTime rewardMonth = DateFormat(yearMonthFormat).parse(month);
    DateTime startDate = DateTime(rewardMonth.year, rewardMonth.month, 1);
    DateTime endDate = DateTime(rewardMonth.year, rewardMonth.month + 1, 0);
    emit(state.copyWith(
        startDate: startDate, endDate: endDate, selectedDateType: ReportQueryPeriodBase.CONFIRMATION_DATE));
  }

  void updateCustomName(String customName) {
    emit(state.copyWith(customName: customName));
  }

  void updateInvoiceId(String invoiceId) {
    emit(state.copyWith(invoiceId: invoiceId));
  }

  void updateCampaigns(List<Item> campaigns) {
    emit(state.copyWith(campaigns: campaigns));
  }

  void clear() {
    emit(FilterState());
  }

  Future<void> initSearchCondition(List<ReportPeriod> periods, bool showCampaigns) async {
    final String currentCustomName = state.customName;
    final String currentInvoiceId = state.invoiceId;

    ReportPeriod selectedPeriod = periods.contains(state.selectedPeriod) ? state.selectedPeriod : periods[0];
    DateTimeRange range = getTimeRange(selectedPeriod, state.startDate, state.endDate);

    emit(FilterState().copyWith(
        customName: currentCustomName,
        invoiceId: currentInvoiceId,
        campaigns: showCampaigns
            ? [Item(value: 0, name: 'All')] + await findAffiliatedRejectedProhibitedCampaignNamesAndIdsForPublisher()
            : [],
        periods: periods,
        selectedPeriod: selectedPeriod,
        startDate: range.start,
        endDate: range.end));
  }

  Future<List<Item>> findAffiliatedRejectedProhibitedCampaignNamesAndIdsForPublisher() async {
    try {
      List<dynamic> result = await campaignRepository.findAffiliatedRejectedProhibitedCampaignNamesAndIdsForPublisher();
      return result.map((e) => Item.fromJson(e)).toList();
    } on DioException catch (e) {
      handleError(e, (message) => emit(state.copyWith(errorMessage: message)));
      return [];
    }
  }
}

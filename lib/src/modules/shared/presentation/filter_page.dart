import 'dart:async';

import 'package:custom_date_range_picker/custom_date_range_picker.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_modular/flutter_modular.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:koc_app/src/base/base_page_state.dart';
import 'package:koc_app/src/modules/account/cubit/site_cubit.dart';
import 'package:koc_app/src/modules/account/data/model/publisher_sites.dart';
import 'package:koc_app/src/modules/report/data/model/report_model.dart';
import 'package:koc_app/src/modules/shared/cubit/filter_cubit.dart';
import 'package:koc_app/src/modules/shared/cubit/filter_state.dart';
import 'package:koc_app/src/shared/constants/color_constants.dart';
import 'package:koc_app/src/shared/data/item.dart';
import 'package:koc_app/src/shared/extensions.dart';
import 'package:koc_app/src/shared/widgets/common_elevated_button.dart';
import 'package:koc_app/src/shared/widgets/social_media_icon.dart';

class FilterPage extends StatefulWidget {
  final List<ReportPeriod> periods;
  final bool showDateType;
  final bool showStatus;
  final bool showSites;
  final bool showCampaigns;
  final bool showInvoiceNumber;
  final bool showCustomName;
  final String filterButtonName;
  final bool preserveState;
  const FilterPage(this.periods, this.showDateType, this.showStatus, this.showSites, this.showCampaigns,
      this.showInvoiceNumber, this.showCustomName,
      {this.filterButtonName = 'Show report', 
      this.preserveState = false,
      super.key});

  @override
  State<FilterPage> createState() => _FilterPageState();
}

class _FilterPageState extends BasePageState<FilterPage, FilterCubit> {
  final TextEditingController customNameController = TextEditingController();
  final TextEditingController invoiceNumberController = TextEditingController();
  bool _showCampaignsSection = true;
  StreamSubscription? _subscription;

  @override
  void initState() {
    initData();
    super.initState();
    _subscription = cubit.stream.listen((state) async {
      if (state.selectedSite != null) {
        await Future.delayed(Duration.zero);
        if (mounted) {
          setState(() {
            _showCampaignsSection = cubit.state.isSearchEnabled;
          });
        }
      }
    });
  }

  Future<void> initData() async {
    if (cubit.state.customName.isNotEmpty) {
      customNameController.text = cubit.state.customName;
    }
    if (cubit.state.invoiceId.isNotEmpty) {
      invoiceNumberController.text = cubit.state.invoiceId;
    }

    if (cubit.state.campaigns.isEmpty) {
      cubit.showLoading();
      await cubit.initSearchCondition(widget.periods, widget.showCampaigns);
      cubit.hideLoading();
    }

    if (cubit.state.selectedSite == null) {
      SiteCubit siteCubit = Modular.get<SiteCubit>();
      if (siteCubit.state.currentSiteId != 0) {
        List<PublisherSite> sites = siteCubit.state.sites;
        cubit.selectSite(Item(
            value: siteCubit.state.currentSiteId,
            name: sites.where((site) => site.id == siteCubit.state.currentSiteId).first.name));
      }
    }
  }

  @override
  Widget buildPage(BuildContext context) {
    return Scaffold(
      appBar: _buildAppBar(),
      body: _buildBody(),
      bottomSheet: _buildBottomSheet(),
    );
  }

@override
  void dispose() {
    _subscription?.cancel();
    customNameController.dispose();
    invoiceNumberController.dispose();
    super.dispose();
  }

  Widget _buildBottomSheet() {
    return Container(
      color: Colors.white,
      padding: EdgeInsets.all(16.r),
      child: Row(
        spacing: 16.r,
        children: [
          Expanded(
            child: SizedBox(
              height: 40.r,
              child: ElevatedButton(
                style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.white,
                    shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(20.r),
                        side: BorderSide(
                          width: 1.r,
                          color: const Color(0xFFAAAAAA),
                        )),
                    padding: EdgeInsets.symmetric(horizontal: 8.r)),
                onPressed: () {
                  _resetFilter();
                },
                child:
                    Text('Clear', style: Theme.of(context).textTheme.labelLarge!.copyWith(fontWeight: FontWeight.w500)),
              ),
            ),
          ),
          Expanded(
              child: BlocBuilder<FilterCubit, FilterState>(
                  bloc: cubit,
                  builder: (_, state) {
                    return CommonElevatedButton(
                        widget.filterButtonName,
                        (state.selectedPeriod != ReportPeriod.CUSTOM_RANGE ||
                                (state.selectedPeriod == ReportPeriod.CUSTOM_RANGE &&
                                    state.startDate != null &&
                                    state.endDate != null)) && state.isSearchEnabled
                            ? () {
                                Modular.to.pop(true);
                              }
                            : null);
                  })),
        ],
      ),
    );
  }

  Widget _buildBody() {
    return SingleChildScrollView(
      child: Container(
        padding: EdgeInsets.only(left: 16.r, right: 16.r, top: 8.r, bottom: 80.r),
        color: Colors.white,
        child: Column(
          spacing: 16.r,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            if (widget.showCustomName)
              _buildContents(
                _buildTitle('Custom name'),
                _buildCustomNameBody(),
              ),
            if (widget.showSites)
              _buildContents(
                _buildTitle('Property'),
                _buildSiteBody(),
              ),
            _buildContents(
              _buildTitle('Period'),
              _buildPeriodBody(),
            ),
            if (widget.showDateType)
              _buildContents(
                _buildTitle('Date type'),
                _buildDateTypeBody(),
              ),
            if (widget.showStatus)
              _buildContents(
                _buildTitle('Status'),
                _buildStatusBody(),
              ),
            if (widget.showCampaigns && _showCampaignsSection)
              _buildContents(
                _buildTitle('Campaign'),
                _buildCampaignBody(),
              ),
            if (widget.showInvoiceNumber)
              _buildContents(
                _buildTitle('Invoice'),
                _buildInvoiceNumberBody(),
              ),
          ],
        ),
      ),
    );
  }

  Widget _buildCustomNameBody() {
    return TextField(
      style: context.textLabelLarge(),
      onChanged: (value) {
        cubit.updateCustomName(value);
      },
      controller: customNameController,
      decoration: InputDecoration(
        hintStyle: context.textLabelLarge(),
        hintText: 'Custom name',
      ),
    );
  }

  Widget _buildInvoiceNumberBody() {
    return TextField(
      style: context.textLabelLarge(),
      onChanged: (value) {
        cubit.updateInvoiceId(value);
      },
      controller: invoiceNumberController,
      decoration: InputDecoration(
        hintStyle: context.textLabelLarge(),
        hintText: 'Invoice number',
      ),
    );
  }

  Widget _buildSiteBody() {
    SiteCubit siteCubit = Modular.get<SiteCubit>();
    return ClipRRect(
      borderRadius: BorderRadius.circular(12.r),
      child: Container(
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(12.r),
          border: Border.all(color: ColorConstants.borderColor),
        ),
        child: BlocBuilder<FilterCubit, FilterState>(builder: (_, state) {
          return Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: siteCubit.state.sites.map((data) {
              int index = siteCubit.state.sites.indexOf(data);
              return GestureDetector(
                onTap: () {
                  cubit.selectSite(Item(value: data.id, name: data.name));
                },
                child: Container(
                  color:
                      _getColor((state.selectedSite == null && data.id == 0) || state.selectedSite?.value == data.id),
                  child: Column(
                    mainAxisSize: MainAxisSize.max,
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Padding(
                        padding: EdgeInsets.symmetric(horizontal: 12.r, vertical: 8.r),
                        child: Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            Row(
                              children: [
                                SizedBox(
                                  height: 30.r,
                                  width: 30.r,
                                  child: SocialMediaIcon(url: data.url),
                                ),
                                SizedBox(
                                    width: 8
                                        .r),
                                Text(
                                  data.name,
                                  style: Theme.of(context)
                                      .textTheme
                                      .labelLarge!
                                      .copyWith(fontWeight: FontWeight.w500),
                                ),
                              ],
                            ),
                            if (siteCubit.state.currentSiteId == data.id)
                              Container(
                                decoration: BoxDecoration(
                                  color: const Color(0xFF1AAA55),
                                  borderRadius: BorderRadius.circular(9999.r),
                                ),
                                padding: EdgeInsets.symmetric(horizontal: 4.r),
                                child: Text('Default',
                                    style: context.textLabelMedium(
                                        color: Colors.white,
                                        fontWeight: FontWeight.w500)),
                              ),
                          ],
                        ),
                      ),
                      if (index < siteCubit.state.sites.length)
                        Divider(
                          color: ColorConstants.borderColor,
                          height: 1.r,
                        ),
                    ],
                  ),
                ),
              );
            }).toList(),
          );
        }),
      ),
    );
  }

  Widget _buildCampaignBody() {
    return ClipRRect(
      borderRadius: BorderRadius.circular(12.r),
      child: Container(
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(12.r),
          border: Border.all(color: ColorConstants.borderColor),
        ),
        child: BlocBuilder<FilterCubit, FilterState>(builder: (_, state) {
          return Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: state.campaigns.map((data) {
              int index = state.campaigns.indexOf(data);
              return GestureDetector(
                onTap: () {
                  cubit.selectCampaign(data);
                },
                child: Container(
                  color:
                      _getColor((state.selectedCampaign == null && data.value == 0) || state.selectedCampaign == data),
                  child: Column(
                    mainAxisSize: MainAxisSize.max,
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Padding(
                        padding: EdgeInsets.symmetric(horizontal: 12.r, vertical: 8.r),
                        child: Text(
                          data.name,
                          style: Theme.of(context).textTheme.labelLarge!.copyWith(fontWeight: FontWeight.w500),
                        ),
                      ),
                      if (index < state.campaigns.length)
                        Divider(
                          color: ColorConstants.borderColor,
                          height: 1.r,
                        ),
                    ],
                  ),
                ),
              );
            }).toList(),
          );
        }),
      ),
    );
  }

  Widget _buildStatusBody() {
    return ClipRRect(
      borderRadius: BorderRadius.circular(12.r),
      child: Container(
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(12.r),
          border: Border.all(color: ColorConstants.borderColor),
        ),
        child: IntrinsicHeight(
          child: BlocBuilder<FilterCubit, FilterState>(builder: (_, state) {
            List<String> statuses = ['All'] + ConversionStatus.values.map((data) => data.name.toTitleCase()).toList();
            return Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: statuses.map((data) {
                int index = statuses.indexOf(data);
                return Expanded(
                    child: Row(
                  children: [
                    Expanded(child: _buildStatusColumnData(data, state.selectedStatus)),
                    if (index < statuses.length)
                      VerticalDivider(
                        color: ColorConstants.borderColor,
                        width: 1.r,
                      ),
                  ],
                ));
              }).toList(),
            );
          }),
        ),
      ),
    );
  }

  Widget _buildStatusColumnData(String name, ConversionStatus? selectedStatus) {
    return GestureDetector(
      onTap: () {
        ConversionStatus? status;
        if (name != 'All') {
          status = ConversionStatus.values.firstWhere((status) => status.name.toTitleCase() == name);
        }
        cubit.selectStatus(status);
      },
      child: Container(
        color: _getColor((selectedStatus == null && name == 'All') ||
            (selectedStatus != null && name == selectedStatus.name.toTitleCase())),
        padding: EdgeInsets.symmetric(vertical: 8.r),
        child: Text(
          name,
          style: Theme.of(context).textTheme.labelLarge!.copyWith(fontWeight: FontWeight.w500),
          textAlign: TextAlign.center,
        ),
      ),
    );
  }

  Widget _buildDateTypeColumnData(ReportQueryPeriodBase dateType, ReportQueryPeriodBase selectedDateType) {
    return Expanded(
      child: GestureDetector(
        onTap: () {
          cubit.selectDateType(dateType);
        },
        child: Container(
          color: _getColor(dateType == selectedDateType),
          padding: EdgeInsets.symmetric(vertical: 8.r),
          child: Text(
            dateType.value,
            style: Theme.of(context).textTheme.labelLarge!.copyWith(fontWeight: FontWeight.w500),
            textAlign: TextAlign.center,
          ),
        ),
      ),
    );
  }

  Color _getColor(bool isSelected) {
    return isSelected ? ColorConstants.selectedColor : Colors.transparent;
  }

  Widget _buildDateTypeBody() {
    return ClipRRect(
      borderRadius: BorderRadius.circular(12.r),
      child: Container(
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(12.r),
          border: Border.all(color: ColorConstants.borderColor),
        ),
        child: IntrinsicHeight(
          child: BlocBuilder<FilterCubit, FilterState>(builder: (_, state) {
            return Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                _buildDateTypeColumnData(ReportQueryPeriodBase.CONVERSION_DATE, state.selectedDateType),
                VerticalDivider(
                  color: ColorConstants.borderColor,
                  width: 1.r,
                ),
                _buildDateTypeColumnData(ReportQueryPeriodBase.CONFIRMATION_DATE, state.selectedDateType),
              ],
            );
          }),
        ),
      ),
    );
  }

  AppBar _buildAppBar() {
    return AppBar(
      surfaceTintColor: Colors.white,
      leading: GestureDetector(
        onTap: () {
          _close();
          Navigator.pop(context);
        },
        child: Icon(Icons.close, size: 20.r),
      ),
      title: Text(
        'Filters',
        style: Theme.of(context).textTheme.titleSmall,
      ),
      centerTitle: true,
    );
  }

  void _resetFilter() async{
    customNameController.clear();
    invoiceNumberController.clear();
    cubit.clear();
    cubit.initSearchCondition(widget.periods, widget.showCampaigns);
  }

    void _close() async{
    if(!widget.preserveState){
      _resetFilter();
    }
  }

  Widget _buildContents(Widget title, Widget body) {
    return Column(
      spacing: 8.r,
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        title,
        body,
      ],
    );
  }

  Widget _buildPeriodBody() {
    return ClipRRect(
      borderRadius: BorderRadius.circular(12.r),
      child: Container(
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(12.r),
          border: Border.all(color: ColorConstants.borderColor),
        ),
        child: BlocBuilder<FilterCubit, FilterState>(builder: (_, state) {
          return Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: state.periods.map((data) {
              int index = state.periods.indexOf(data);
              return GestureDetector(
                onTap: () async {
                  if (data == ReportPeriod.CUSTOM_RANGE) {
                    _showDateRangePicker();
                  } else {
                    cubit.selectPeriod(data);
                  }
                },
                child: Container(
                  color: _getColor(state.selectedPeriod == data),
                  child: Column(
                    mainAxisSize: MainAxisSize.max,
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Padding(
                        padding: EdgeInsets.symmetric(horizontal: 12.r, vertical: 8.r),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              data.value,
                              style: Theme.of(context).textTheme.labelLarge!.copyWith(fontWeight: FontWeight.w500),
                            ),
                            if (ReportPeriod.CUSTOM_RANGE == data &&
                                ReportPeriod.CUSTOM_RANGE == state.selectedPeriod &&
                                state.customStartDate != null &&
                                state.customEndDate != null)
                              Text(
                                '${state.customStartDate!.toFormattedString()} - ${state.customEndDate!.toFormattedString()}',
                                style: Theme.of(context).textTheme.labelLarge!.copyWith(fontWeight: FontWeight.w500),
                              )
                          ],
                        ),
                      ),
                      if (index < state.periods.length)
                        Divider(
                          color: ColorConstants.borderColor,
                          height: 1.r,
                        ),
                    ],
                  ),
                ),
              );
            }).toList(),
          );
        }),
      ),
    );
  }

  Widget _buildTitle(String title) {
    return Text(
      title,
      style: Theme.of(context).textTheme.labelLarge!.copyWith(fontWeight: FontWeight.bold),
    );
  }

  Future<void> _showDateRangePicker() async {
    DateTime today = DateTime.now();
    showCustomDateRangePicker(
      context,
      dismissible: true,
      minimumDate: today.subtract(const Duration(days: 395)),
      maximumDate: today,
      endDate: cubit.state.customEndDate,
      startDate: cubit.state.customStartDate,
      backgroundColor: Colors.white,
      primaryColor: const Color(0xFFEF6507),
      onApplyClick: (start, end) {
        cubit.selectCustomRangeDate(start, end);
      },
      onCancelClick: () {},
    );
  }
}

import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:koc_app/src/modules/report/data/model/report_model.dart';
import 'package:koc_app/src/modules/shared/cubit/filter_state.dart';
import 'package:koc_app/src/modules/shared/presentation/filter_page.dart';
import 'package:koc_app/src/shared/constants/color_constants.dart';
import 'package:koc_app/src/shared/extensions.dart';

mixin FilterMixin {
  Future<bool?> showFilters(BuildContext context, List<ReportPeriod> periods,
      {bool showDateType = true,
      bool showStatus = true,
      bool showSites = false,
      bool showCampaigns = true,
      bool showInvoiceNumber = false,
      bool showCustomName = false,
      String filterButtonName = 'Show report',
      bool preserveState = false }) async {
    return await showModalBottomSheet<bool>(
      context: context,
      useSafeArea: true,
      isScrollControlled: true,
      builder: (_) {
        return ClipRRect(
          borderRadius: BorderRadius.circular(12.r),
          child: FilterPage(
              periods, showDateType, showStatus, showSites, showCampaigns, showInvoiceNumber, showCustomName,
              filterButtonName: filterButtonName,
              preserveState: preserveState,),
        );
      },
    );
  }

  Widget buildFilterRow(BuildContext context, FilterState state,
      {bool showSites = false, bool showCampaigns = true, bool showInvoiceNumber = false}) {
    return SingleChildScrollView(
      scrollDirection: Axis.horizontal,
      child: Padding(
        padding: EdgeInsets.all(8.r),
        child: Row(
          spacing: 8.r,
          children: [
            _buildFilterChip(
                context, '${state.startDate?.toFormattedString()} - ${state.endDate?.toFormattedString()}'),
            _buildFilterChip(context, state.selectedDateType.value),
            _buildFilterChip(context, '${state.selectedStatus?.name.toTitleCase() ?? 'All'} status'),
            if (showCampaigns) _buildFilterChip(context, '${state.selectedCampaign?.name ?? 'All'} campaign'),
            if (showSites) _buildFilterChip(context, '${state.selectedSite?.name ?? 'All'} site'),
          ],
        ),
      ),
    );
  }

  Widget _buildFilterChip(BuildContext context, String name) {
    return Container(
      padding: EdgeInsets.symmetric(horizontal: 8.r, vertical: 4.r),
      decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(9999),
          color: const Color(0xFFF5F5F7),
          border: Border.all(width: 1.r, color: ColorConstants.borderColor)),
      child: Text(
        name,
        style: Theme.of(context).textTheme.labelMedium!.copyWith(fontWeight: FontWeight.w500),
      ),
    );
  }

  DateTimeRange getTimeRange(ReportPeriod selectedPeriod, DateTime? customStartDate, DateTime? customEndDate) {
    DateTime startDate;
    DateTime endDate;
    DateTime now = DateTime.now();
    DateTime today = DateTime(now.year, now.month, now.day);
    if (selectedPeriod == ReportPeriod.YESTERDAY) {
      startDate = today.subtract(const Duration(days: 1));
      endDate = startDate;
    } else if (selectedPeriod == ReportPeriod.LAST_7_DAYS) {
      endDate = today;
      startDate = endDate.subtract(const Duration(days: 7));
    } else if (selectedPeriod == ReportPeriod.LAST_14_DAYS) {
      endDate = today;
      startDate = endDate.subtract(const Duration(days: 14));
    } else if (selectedPeriod == ReportPeriod.LAST_30_DAYS) {
      endDate = today;
      startDate = endDate.subtract(const Duration(days: 30));
    } else if (selectedPeriod == ReportPeriod.THIS_WEEK) {
      startDate = DateTime(today.year, today.month, today.day - today.weekday + 1);
      endDate = today;
    } else if (selectedPeriod == ReportPeriod.LAST_WEEK) {
      startDate = DateTime(today.year, today.month, today.day - today.weekday - 6);
      endDate = DateTime(today.year, today.month, today.day - today.weekday);
    } else if (selectedPeriod == ReportPeriod.THIS_MONTH) {
      startDate = DateTime(today.year, today.month, 1);
      endDate = today;
    } else if (selectedPeriod == ReportPeriod.LAST_MONTH) {
      startDate = DateTime(today.year, today.month - 1, 1);
      endDate = DateTime(today.year, today.month, 1).subtract(const Duration(days: 1));
    } else if (selectedPeriod == ReportPeriod.LAST_12_MONTHS) {
      endDate = today;
      startDate = endDate.subtract(const Duration(days: 365));
    } else if (selectedPeriod == ReportPeriod.LAST_3_MONTHS) {
      endDate = today;
      startDate = endDate.subtract(const Duration(days: 90));
    } else if (selectedPeriod == ReportPeriod.LAST_YEAR) {
      endDate = DateTime(today.year, 1, 1).subtract(const Duration(days: 1));
      startDate = DateTime(today.year - 1, 1, 1);
    } else if (selectedPeriod == ReportPeriod.THIS_YEAR) {
      startDate = DateTime(today.year, 1, 1);
      endDate = DateTime(today.year, today.month + 1, 0);
    } else if (selectedPeriod == ReportPeriod.CUSTOM_RANGE) {
      startDate = customStartDate!;
      endDate = customEndDate!;
    } else {
      startDate = today;
      endDate = today;
    }
    return DateTimeRange(start: startDate, end: endDate);
  }
}

import 'dart:async';

import 'package:flutter_modular/flutter_modular.dart';
import 'package:koc_app/src/base/cubit/base_cubit.dart';
import 'package:koc_app/src/modules/authentication/cubit/authentication_cubit.dart';
import 'package:koc_app/src/modules/authentication/data/models/auth_token_info.dart';
import 'package:koc_app/src/modules/authentication/data/models/sign_in_sign_up.dart';
import 'package:koc_app/src/modules/authentication/data/repository/authentication_repository.dart';
import 'package:koc_app/src/modules/survey/cubit/survey_state.dart';
import 'package:koc_app/src/modules/survey/data/models/survey_info.dart';
import 'package:koc_app/src/modules/survey/data/repository/survey_repository.dart';
import 'package:koc_app/src/shared/constants/instance_key.dart';
import 'package:koc_app/src/shared/services/url_helpers.dart';
import 'package:koc_app/src/shared/utils/handle_error.dart';

import '../../../shared/services/shared_preferences_service.dart';

class SurveyCubit extends BaseCubit<SurveyState> {
  final SurveyRepository surveyRepository;
  final AuthenticationCubit authenticationCubit;
  final SharedPreferencesService sharedPreferencesService = Modular.get<SharedPreferencesService>();
  final AuthenticationRepository _authenticationRepository = Modular.get<AuthenticationRepository>();

  SurveyCubit(this.surveyRepository, this.authenticationCubit) : super(SurveyState());

  void saveUserInfo(UserInfo userInfo) {
    emit(state.copyWith(userInfo: userInfo));
  }

  void saveSocialInfo(SocialInfo socialInfo) {
    emit(state.copyWith(socialInfo: socialInfo));
  }

  void clearSocialInfo() {
    emit(state.copyWith(socialInfo: const SocialInfo()));
  }

  Future<bool> _isSocialSignIn() async {
    final googleTokens = await authenticationCubit.getGoogleSignInTokensFromStorage();
    final googleIdToken = googleTokens?.idToken;
    final userEmail = await sharedPreferencesService.getEmail();

    if (googleIdToken != null && googleIdToken.isNotEmpty && googleTokens?.email != null) {
      if (userEmail == googleTokens?.email) {
        return true;
      }
    }

    final facebookResponse = await authenticationCubit.getFacebookAuthResponseFromStorage();
    if (facebookResponse != null && facebookResponse.accessToken != null && facebookResponse.id.isNotEmpty) {
      final storedFacebookId = await sharedPreferencesService.getStringFromInstance(InstanceConstants.facebookIdKey);
      if (storedFacebookId != null && storedFacebookId.isNotEmpty && storedFacebookId == facebookResponse.id) {
        return true;
      }
    }

    return false;
  }

  Future<bool> _handleSocialSignIn() async {
    try {
      showLoading();

      final userInfo = state.userInfo;
      final socialInfo = state.socialInfo;
      final userEmail = await sharedPreferencesService.getEmail();
      final userCountryCode = await sharedPreferencesService.getCountryCode();

      if (userEmail == null || userEmail.isEmpty) {
        emit(state.copyWith(errorMessage: 'Invalid Email'));
        return false;
      }

      if (userCountryCode == null || userCountryCode.isEmpty) {
        emit(state.copyWith(errorMessage: 'Invalid Country Code'));
        return false;
      }

      final googleSecureData = await authenticationCubit.getGoogleSignInTokensFromStorage();
      if (googleSecureData != null && googleSecureData.accessToken != null && googleSecureData.email == userEmail) {
        emit(state.copyWith(
            accessToken: googleSecureData.accessToken!, socialNetworkType: SocialNetworkType.google.value));
      } else {
        final facebookResponse = await authenticationCubit.getFacebookAuthResponseFromStorage();
        if (facebookResponse != null && facebookResponse.accessToken != null && facebookResponse.id.isNotEmpty) {
          final storedFacebookId =
              await sharedPreferencesService.getStringFromInstance(InstanceConstants.facebookIdKey);
          if (storedFacebookId != null && storedFacebookId.isNotEmpty && storedFacebookId == facebookResponse.id) {
            emit(state.copyWith(
                accessToken: facebookResponse.accessToken!, socialNetworkType: SocialNetworkType.facebook.value));
          } else {
            return false;
          }
        } else {
          return false;
        }
      }

      // TODO: Check TikTok when implemented
      // final tiktokData = await authenticationCubit.getTikTokTokensFromStorage();
      // if (tiktokData != null && tiktokData.accessToken != null && tiktokData.email == userEmail) {
      //   emit(state.copyWith(
      //       accessToken: tiktokData.accessToken!, socialNetworkType: SocialNetworkType.tiktok.value));
      // }

      if (state.socialNetworkType.isEmpty || state.accessToken.isEmpty) {
        emit(state.copyWith(errorMessage: 'Social sign-in data not found'));
        return false;
      }

      final socialNetwork = await getSocialTypeFromUrl(socialInfo.url);
      final String profilePicture = await sharedPreferencesService.getProfilePictureUrl() ?? '';

      final socialSignInPayload = SocialSignInPayload(
        accessToken: state.accessToken,
        countryCode: userCountryCode,
        socialNetworkType: state.socialNetworkType,
        firstName: userInfo.firstName,
        lastName: userInfo.lastName,
        siteName: socialInfo.name,
        siteUrl: socialInfo.url,
        socialNetwork: socialNetwork.name,
        totalFollowerLevel: socialInfo.totalFollowerLevel.name,
        profilePictureUrl: profilePicture
      );

      final result = await _authenticationRepository.socialLogin(socialSignInPayload);
      final authData = AuthTokenInfo.fromJson(result);
      await saveTokenAndSetData(authData);
      return true;
    } catch (e) {
      handleError(e, (message) => emit(state.copyWith(errorMessage: message)));
      return false;
    } finally {
      hideLoading();
    }
  }

  Future<bool> saveState() async {
    try {
      final isSocialSignIn = await _isSocialSignIn();

      if (isSocialSignIn) {
        return await _handleSocialSignIn();
      } else {
        final userInfo = state.userInfo;
        final socialInfo = state.socialInfo;
        final userEmail = await sharedPreferencesService.getEmail();
        final userCountryCode = await sharedPreferencesService.getCountryCode();

        if (userEmail == null || userEmail.isEmpty) {
          emit(state.copyWith(errorMessage: 'Invalid Email'));
          return false;
        }

        if (userCountryCode == null || userCountryCode.isEmpty) {
          emit(state.copyWith(errorMessage: 'Invalid Country Code'));
          return false;
        }

        final socialMediaType = getSocialTypeFromUrl(socialInfo.url);

        AddPartnerRequest request = AddPartnerRequest(
            email: userEmail,
            countryCode: userCountryCode,
            firstName: userInfo.firstName,
            lastName: userInfo.lastName,
            siteName: socialInfo.name,
            siteUrl: socialInfo.url,
            socialMediaType: socialMediaType.name);

        request = request.copyWith(socialMediaFollower: socialInfo.totalFollowerLevel.name);

        final result = await surveyRepository.addPartner(request);
        AuthTokenInfo authTokenInfo = AuthTokenInfo.fromJson(result);
        await saveTokenAndSetData(authTokenInfo);
        return true;
      }
    } catch (e) {
      handleError(e, (message) => emit(state.copyWith(errorMessage: message)));
      return false;
    }
  }

  Future<void> saveTokenAndSetData(AuthTokenInfo authTokenInfo) async {
    await Future.wait(
        [commonCubit.saveToken(authTokenInfo), authenticationCubit.getSites(), authenticationCubit.getCurrency()]);
  }

}

// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'auth_token_info.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$AuthTokenInfoImpl _$$AuthTokenInfoImplFromJson(Map<String, dynamic> json) =>
    _$AuthTokenInfoImpl(
      token: json['token'] as String,
      expiresIn: (json['expiresIn'] as num).toInt(),
      refreshExpiresIn: (json['refreshExpiresIn'] as num).toInt(),
      refreshToken: json['refreshToken'] as String,
      tokenType: json['tokenType'] as String?,
      notBeforePolicy: (json['notBeforePolicy'] as num?)?.toInt(),
      sessionState: json['sessionState'] as String?,
      scope: json['scope'] as String?,
    );

Map<String, dynamic> _$$AuthTokenInfoImplToJson(_$AuthTokenInfoImpl instance) =>
    <String, dynamic>{
      'token': instance.token,
      'expiresIn': instance.expiresIn,
      'refreshExpiresIn': instance.refreshExpiresIn,
      'refreshToken': instance.refreshToken,
      'tokenType': instance.tokenType,
      'notBeforePolicy': instance.notBeforePolicy,
      'sessionState': instance.sessionState,
      'scope': instance.scope,
    };

_$RefreshTokenRequestImpl _$$RefreshTokenRequestImplFromJson(
        Map<String, dynamic> json) =>
    _$RefreshTokenRequestImpl(
      refreshToken: json['refreshToken'] as String,
      clientId: json['clientId'] as String,
    );

Map<String, dynamic> _$$RefreshTokenRequestImplToJson(
        _$RefreshTokenRequestImpl instance) =>
    <String, dynamic>{
      'refreshToken': instance.refreshToken,
      'clientId': instance.clientId,
    };

_$FacebookAuthResponseImpl _$$FacebookAuthResponseImplFromJson(
        Map<String, dynamic> json) =>
    _$FacebookAuthResponseImpl(
      id: json['id'] as String,
      first_name: json['first_name'] as String?,
      last_name: json['last_name'] as String?,
      name: json['name'] as String?,
      email: json['email'] as String?,
      picture: json['picture'] == null
          ? null
          : FacebookPicture.fromJson(json['picture'] as Map<String, dynamic>),
      accessToken: json['accessToken'] as String?,
    );

Map<String, dynamic> _$$FacebookAuthResponseImplToJson(
        _$FacebookAuthResponseImpl instance) =>
    <String, dynamic>{
      'id': instance.id,
      'first_name': instance.first_name,
      'last_name': instance.last_name,
      'name': instance.name,
      'email': instance.email,
      'picture': instance.picture,
      'accessToken': instance.accessToken,
    };

_$FacebookPictureImpl _$$FacebookPictureImplFromJson(
        Map<String, dynamic> json) =>
    _$FacebookPictureImpl(
      data: FacebookPictureData.fromJson(json['data'] as Map<String, dynamic>),
    );

Map<String, dynamic> _$$FacebookPictureImplToJson(
        _$FacebookPictureImpl instance) =>
    <String, dynamic>{
      'data': instance.data,
    };

_$FacebookPictureDataImpl _$$FacebookPictureDataImplFromJson(
        Map<String, dynamic> json) =>
    _$FacebookPictureDataImpl(
      height: (json['height'] as num).toInt(),
      is_silhouette: json['is_silhouette'] as bool,
      url: json['url'] as String,
      width: (json['width'] as num).toInt(),
    );

Map<String, dynamic> _$$FacebookPictureDataImplToJson(
        _$FacebookPictureDataImpl instance) =>
    <String, dynamic>{
      'height': instance.height,
      'is_silhouette': instance.is_silhouette,
      'url': instance.url,
      'width': instance.width,
    };

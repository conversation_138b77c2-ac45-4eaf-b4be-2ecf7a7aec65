import 'package:freezed_annotation/freezed_annotation.dart';

part 'auth_token_info.freezed.dart';
part 'auth_token_info.g.dart';

@freezed
class AuthTokenInfo with _$AuthTokenInfo {
  const factory AuthTokenInfo({
    required String token,
    required int expiresIn,
    required int refreshExpiresIn,
    required String refreshToken,
    String? tokenType,
    int? notBeforePolicy,
    String? sessionState,
    String? scope,
  }) = _AuthTokenInfo;

  factory AuthTokenInfo.fromJson(Map<String, dynamic> json) =>
      _$AuthTokenInfoFromJson(json);
}

@freezed
class RefreshTokenRequest with _$RefreshTokenRequest {
  factory RefreshTokenRequest({
    required String refreshToken,
    required String clientId,
  }) = _RefreshTokenRequest;

  factory RefreshTokenRequest.fromJson(Map<String, dynamic> json) => _$RefreshTokenRequestFromJson(json);
}


@freezed
class FacebookAuthResponse with _$FacebookAuthResponse {
  const factory FacebookAuthResponse({
    required String id,
    String? first_name,
    String? last_name,
    String? name,
    String? email,
    FacebookPicture? picture,
    String? accessToken,
  }) = _FacebookAuthResponse;

  factory FacebookAuthResponse.fromJson(Map<String, dynamic> json) => 
      _$FacebookAuthResponseFromJson(json);
}

@freezed
class FacebookPicture with _$FacebookPicture {
  const factory FacebookPicture({
    required FacebookPictureData data,
  }) = _FacebookPicture;

  factory FacebookPicture.fromJson(Map<String, dynamic> json) => 
      _$FacebookPictureFromJson(json);
}

@freezed
class FacebookPictureData with _$FacebookPictureData {
  const factory FacebookPictureData({
    required int height,
    required bool is_silhouette,
    required String url,
    required int width,
  }) = _FacebookPictureData;

  factory FacebookPictureData.fromJson(Map<String, dynamic> json) => 
      _$FacebookPictureDataFromJson(json);
}
// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'sign_in_sign_up.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

SignInRequest _$SignInRequestFromJson(Map<String, dynamic> json) {
  return _SignInRequest.fromJson(json);
}

/// @nodoc
mixin _$SignInRequest {
  String get username => throw _privateConstructorUsedError;
  String get password => throw _privateConstructorUsedError;
  String get countryCode => throw _privateConstructorUsedError;
  String get clientId => throw _privateConstructorUsedError;

  /// Serializes this SignInRequest to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of SignInRequest
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $SignInRequestCopyWith<SignInRequest> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $SignInRequestCopyWith<$Res> {
  factory $SignInRequestCopyWith(
          SignInRequest value, $Res Function(SignInRequest) then) =
      _$SignInRequestCopyWithImpl<$Res, SignInRequest>;
  @useResult
  $Res call(
      {String username, String password, String countryCode, String clientId});
}

/// @nodoc
class _$SignInRequestCopyWithImpl<$Res, $Val extends SignInRequest>
    implements $SignInRequestCopyWith<$Res> {
  _$SignInRequestCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of SignInRequest
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? username = null,
    Object? password = null,
    Object? countryCode = null,
    Object? clientId = null,
  }) {
    return _then(_value.copyWith(
      username: null == username
          ? _value.username
          : username // ignore: cast_nullable_to_non_nullable
              as String,
      password: null == password
          ? _value.password
          : password // ignore: cast_nullable_to_non_nullable
              as String,
      countryCode: null == countryCode
          ? _value.countryCode
          : countryCode // ignore: cast_nullable_to_non_nullable
              as String,
      clientId: null == clientId
          ? _value.clientId
          : clientId // ignore: cast_nullable_to_non_nullable
              as String,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$SignInRequestImplCopyWith<$Res>
    implements $SignInRequestCopyWith<$Res> {
  factory _$$SignInRequestImplCopyWith(
          _$SignInRequestImpl value, $Res Function(_$SignInRequestImpl) then) =
      __$$SignInRequestImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {String username, String password, String countryCode, String clientId});
}

/// @nodoc
class __$$SignInRequestImplCopyWithImpl<$Res>
    extends _$SignInRequestCopyWithImpl<$Res, _$SignInRequestImpl>
    implements _$$SignInRequestImplCopyWith<$Res> {
  __$$SignInRequestImplCopyWithImpl(
      _$SignInRequestImpl _value, $Res Function(_$SignInRequestImpl) _then)
      : super(_value, _then);

  /// Create a copy of SignInRequest
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? username = null,
    Object? password = null,
    Object? countryCode = null,
    Object? clientId = null,
  }) {
    return _then(_$SignInRequestImpl(
      username: null == username
          ? _value.username
          : username // ignore: cast_nullable_to_non_nullable
              as String,
      password: null == password
          ? _value.password
          : password // ignore: cast_nullable_to_non_nullable
              as String,
      countryCode: null == countryCode
          ? _value.countryCode
          : countryCode // ignore: cast_nullable_to_non_nullable
              as String,
      clientId: null == clientId
          ? _value.clientId
          : clientId // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$SignInRequestImpl implements _SignInRequest {
  const _$SignInRequestImpl(
      {required this.username,
      required this.password,
      required this.countryCode,
      required this.clientId});

  factory _$SignInRequestImpl.fromJson(Map<String, dynamic> json) =>
      _$$SignInRequestImplFromJson(json);

  @override
  final String username;
  @override
  final String password;
  @override
  final String countryCode;
  @override
  final String clientId;

  @override
  String toString() {
    return 'SignInRequest(username: $username, password: $password, countryCode: $countryCode, clientId: $clientId)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$SignInRequestImpl &&
            (identical(other.username, username) ||
                other.username == username) &&
            (identical(other.password, password) ||
                other.password == password) &&
            (identical(other.countryCode, countryCode) ||
                other.countryCode == countryCode) &&
            (identical(other.clientId, clientId) ||
                other.clientId == clientId));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode =>
      Object.hash(runtimeType, username, password, countryCode, clientId);

  /// Create a copy of SignInRequest
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$SignInRequestImplCopyWith<_$SignInRequestImpl> get copyWith =>
      __$$SignInRequestImplCopyWithImpl<_$SignInRequestImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$SignInRequestImplToJson(
      this,
    );
  }
}

abstract class _SignInRequest implements SignInRequest {
  const factory _SignInRequest(
      {required final String username,
      required final String password,
      required final String countryCode,
      required final String clientId}) = _$SignInRequestImpl;

  factory _SignInRequest.fromJson(Map<String, dynamic> json) =
      _$SignInRequestImpl.fromJson;

  @override
  String get username;
  @override
  String get password;
  @override
  String get countryCode;
  @override
  String get clientId;

  /// Create a copy of SignInRequest
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$SignInRequestImplCopyWith<_$SignInRequestImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

SocialLoginRequest _$SocialLoginRequestFromJson(Map<String, dynamic> json) {
  return _SocialLoginRequest.fromJson(json);
}

/// @nodoc
mixin _$SocialLoginRequest {
  String get token => throw _privateConstructorUsedError;
  String get provider => throw _privateConstructorUsedError;
  String get countryCode => throw _privateConstructorUsedError;
  String get clientId => throw _privateConstructorUsedError;

  /// Serializes this SocialLoginRequest to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of SocialLoginRequest
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $SocialLoginRequestCopyWith<SocialLoginRequest> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $SocialLoginRequestCopyWith<$Res> {
  factory $SocialLoginRequestCopyWith(
          SocialLoginRequest value, $Res Function(SocialLoginRequest) then) =
      _$SocialLoginRequestCopyWithImpl<$Res, SocialLoginRequest>;
  @useResult
  $Res call(
      {String token, String provider, String countryCode, String clientId});
}

/// @nodoc
class _$SocialLoginRequestCopyWithImpl<$Res, $Val extends SocialLoginRequest>
    implements $SocialLoginRequestCopyWith<$Res> {
  _$SocialLoginRequestCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of SocialLoginRequest
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? token = null,
    Object? provider = null,
    Object? countryCode = null,
    Object? clientId = null,
  }) {
    return _then(_value.copyWith(
      token: null == token
          ? _value.token
          : token // ignore: cast_nullable_to_non_nullable
              as String,
      provider: null == provider
          ? _value.provider
          : provider // ignore: cast_nullable_to_non_nullable
              as String,
      countryCode: null == countryCode
          ? _value.countryCode
          : countryCode // ignore: cast_nullable_to_non_nullable
              as String,
      clientId: null == clientId
          ? _value.clientId
          : clientId // ignore: cast_nullable_to_non_nullable
              as String,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$SocialLoginRequestImplCopyWith<$Res>
    implements $SocialLoginRequestCopyWith<$Res> {
  factory _$$SocialLoginRequestImplCopyWith(_$SocialLoginRequestImpl value,
          $Res Function(_$SocialLoginRequestImpl) then) =
      __$$SocialLoginRequestImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {String token, String provider, String countryCode, String clientId});
}

/// @nodoc
class __$$SocialLoginRequestImplCopyWithImpl<$Res>
    extends _$SocialLoginRequestCopyWithImpl<$Res, _$SocialLoginRequestImpl>
    implements _$$SocialLoginRequestImplCopyWith<$Res> {
  __$$SocialLoginRequestImplCopyWithImpl(_$SocialLoginRequestImpl _value,
      $Res Function(_$SocialLoginRequestImpl) _then)
      : super(_value, _then);

  /// Create a copy of SocialLoginRequest
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? token = null,
    Object? provider = null,
    Object? countryCode = null,
    Object? clientId = null,
  }) {
    return _then(_$SocialLoginRequestImpl(
      token: null == token
          ? _value.token
          : token // ignore: cast_nullable_to_non_nullable
              as String,
      provider: null == provider
          ? _value.provider
          : provider // ignore: cast_nullable_to_non_nullable
              as String,
      countryCode: null == countryCode
          ? _value.countryCode
          : countryCode // ignore: cast_nullable_to_non_nullable
              as String,
      clientId: null == clientId
          ? _value.clientId
          : clientId // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$SocialLoginRequestImpl implements _SocialLoginRequest {
  const _$SocialLoginRequestImpl(
      {required this.token,
      required this.provider,
      required this.countryCode,
      required this.clientId});

  factory _$SocialLoginRequestImpl.fromJson(Map<String, dynamic> json) =>
      _$$SocialLoginRequestImplFromJson(json);

  @override
  final String token;
  @override
  final String provider;
  @override
  final String countryCode;
  @override
  final String clientId;

  @override
  String toString() {
    return 'SocialLoginRequest(token: $token, provider: $provider, countryCode: $countryCode, clientId: $clientId)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$SocialLoginRequestImpl &&
            (identical(other.token, token) || other.token == token) &&
            (identical(other.provider, provider) ||
                other.provider == provider) &&
            (identical(other.countryCode, countryCode) ||
                other.countryCode == countryCode) &&
            (identical(other.clientId, clientId) ||
                other.clientId == clientId));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode =>
      Object.hash(runtimeType, token, provider, countryCode, clientId);

  /// Create a copy of SocialLoginRequest
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$SocialLoginRequestImplCopyWith<_$SocialLoginRequestImpl> get copyWith =>
      __$$SocialLoginRequestImplCopyWithImpl<_$SocialLoginRequestImpl>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$SocialLoginRequestImplToJson(
      this,
    );
  }
}

abstract class _SocialLoginRequest implements SocialLoginRequest {
  const factory _SocialLoginRequest(
      {required final String token,
      required final String provider,
      required final String countryCode,
      required final String clientId}) = _$SocialLoginRequestImpl;

  factory _SocialLoginRequest.fromJson(Map<String, dynamic> json) =
      _$SocialLoginRequestImpl.fromJson;

  @override
  String get token;
  @override
  String get provider;
  @override
  String get countryCode;
  @override
  String get clientId;

  /// Create a copy of SocialLoginRequest
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$SocialLoginRequestImplCopyWith<_$SocialLoginRequestImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

SocialSignInPayload _$SocialSignInPayloadFromJson(Map<String, dynamic> json) {
  return _SocialSignInPayload.fromJson(json);
}

/// @nodoc
mixin _$SocialSignInPayload {
  String get accessToken => throw _privateConstructorUsedError;
  String get countryCode => throw _privateConstructorUsedError;
  String get socialNetworkType => throw _privateConstructorUsedError;
  @JsonKey(includeIfNull: false)
  String? get firstName => throw _privateConstructorUsedError;
  @JsonKey(includeIfNull: false)
  String? get lastName => throw _privateConstructorUsedError;
  @JsonKey(includeIfNull: false)
  String? get siteName => throw _privateConstructorUsedError;
  @JsonKey(includeIfNull: false)
  String? get siteUrl => throw _privateConstructorUsedError;
  @JsonKey(includeIfNull: false)
  String? get socialNetwork => throw _privateConstructorUsedError;
  @JsonKey(includeIfNull: false)
  String? get profilePictureUrl => throw _privateConstructorUsedError;
  @JsonKey(includeIfNull: false)
  String? get tokenType => throw _privateConstructorUsedError;
  String get totalFollowerLevel => throw _privateConstructorUsedError;

  /// Serializes this SocialSignInPayload to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of SocialSignInPayload
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $SocialSignInPayloadCopyWith<SocialSignInPayload> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $SocialSignInPayloadCopyWith<$Res> {
  factory $SocialSignInPayloadCopyWith(
          SocialSignInPayload value, $Res Function(SocialSignInPayload) then) =
      _$SocialSignInPayloadCopyWithImpl<$Res, SocialSignInPayload>;
  @useResult
  $Res call(
      {String accessToken,
      String countryCode,
      String socialNetworkType,
      @JsonKey(includeIfNull: false) String? firstName,
      @JsonKey(includeIfNull: false) String? lastName,
      @JsonKey(includeIfNull: false) String? siteName,
      @JsonKey(includeIfNull: false) String? siteUrl,
      @JsonKey(includeIfNull: false) String? socialNetwork,
      @JsonKey(includeIfNull: false) String? profilePictureUrl,
      @JsonKey(includeIfNull: false) String? tokenType,
      String totalFollowerLevel});
}

/// @nodoc
class _$SocialSignInPayloadCopyWithImpl<$Res, $Val extends SocialSignInPayload>
    implements $SocialSignInPayloadCopyWith<$Res> {
  _$SocialSignInPayloadCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of SocialSignInPayload
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? accessToken = null,
    Object? countryCode = null,
    Object? socialNetworkType = null,
    Object? firstName = freezed,
    Object? lastName = freezed,
    Object? siteName = freezed,
    Object? siteUrl = freezed,
    Object? socialNetwork = freezed,
    Object? profilePictureUrl = freezed,
    Object? tokenType = freezed,
    Object? totalFollowerLevel = null,
  }) {
    return _then(_value.copyWith(
      accessToken: null == accessToken
          ? _value.accessToken
          : accessToken // ignore: cast_nullable_to_non_nullable
              as String,
      countryCode: null == countryCode
          ? _value.countryCode
          : countryCode // ignore: cast_nullable_to_non_nullable
              as String,
      socialNetworkType: null == socialNetworkType
          ? _value.socialNetworkType
          : socialNetworkType // ignore: cast_nullable_to_non_nullable
              as String,
      firstName: freezed == firstName
          ? _value.firstName
          : firstName // ignore: cast_nullable_to_non_nullable
              as String?,
      lastName: freezed == lastName
          ? _value.lastName
          : lastName // ignore: cast_nullable_to_non_nullable
              as String?,
      siteName: freezed == siteName
          ? _value.siteName
          : siteName // ignore: cast_nullable_to_non_nullable
              as String?,
      siteUrl: freezed == siteUrl
          ? _value.siteUrl
          : siteUrl // ignore: cast_nullable_to_non_nullable
              as String?,
      socialNetwork: freezed == socialNetwork
          ? _value.socialNetwork
          : socialNetwork // ignore: cast_nullable_to_non_nullable
              as String?,
      profilePictureUrl: freezed == profilePictureUrl
          ? _value.profilePictureUrl
          : profilePictureUrl // ignore: cast_nullable_to_non_nullable
              as String?,
      tokenType: freezed == tokenType
          ? _value.tokenType
          : tokenType // ignore: cast_nullable_to_non_nullable
              as String?,
      totalFollowerLevel: null == totalFollowerLevel
          ? _value.totalFollowerLevel
          : totalFollowerLevel // ignore: cast_nullable_to_non_nullable
              as String,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$SocialSignInPayloadImplCopyWith<$Res>
    implements $SocialSignInPayloadCopyWith<$Res> {
  factory _$$SocialSignInPayloadImplCopyWith(_$SocialSignInPayloadImpl value,
          $Res Function(_$SocialSignInPayloadImpl) then) =
      __$$SocialSignInPayloadImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {String accessToken,
      String countryCode,
      String socialNetworkType,
      @JsonKey(includeIfNull: false) String? firstName,
      @JsonKey(includeIfNull: false) String? lastName,
      @JsonKey(includeIfNull: false) String? siteName,
      @JsonKey(includeIfNull: false) String? siteUrl,
      @JsonKey(includeIfNull: false) String? socialNetwork,
      @JsonKey(includeIfNull: false) String? profilePictureUrl,
      @JsonKey(includeIfNull: false) String? tokenType,
      String totalFollowerLevel});
}

/// @nodoc
class __$$SocialSignInPayloadImplCopyWithImpl<$Res>
    extends _$SocialSignInPayloadCopyWithImpl<$Res, _$SocialSignInPayloadImpl>
    implements _$$SocialSignInPayloadImplCopyWith<$Res> {
  __$$SocialSignInPayloadImplCopyWithImpl(_$SocialSignInPayloadImpl _value,
      $Res Function(_$SocialSignInPayloadImpl) _then)
      : super(_value, _then);

  /// Create a copy of SocialSignInPayload
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? accessToken = null,
    Object? countryCode = null,
    Object? socialNetworkType = null,
    Object? firstName = freezed,
    Object? lastName = freezed,
    Object? siteName = freezed,
    Object? siteUrl = freezed,
    Object? socialNetwork = freezed,
    Object? profilePictureUrl = freezed,
    Object? tokenType = freezed,
    Object? totalFollowerLevel = null,
  }) {
    return _then(_$SocialSignInPayloadImpl(
      accessToken: null == accessToken
          ? _value.accessToken
          : accessToken // ignore: cast_nullable_to_non_nullable
              as String,
      countryCode: null == countryCode
          ? _value.countryCode
          : countryCode // ignore: cast_nullable_to_non_nullable
              as String,
      socialNetworkType: null == socialNetworkType
          ? _value.socialNetworkType
          : socialNetworkType // ignore: cast_nullable_to_non_nullable
              as String,
      firstName: freezed == firstName
          ? _value.firstName
          : firstName // ignore: cast_nullable_to_non_nullable
              as String?,
      lastName: freezed == lastName
          ? _value.lastName
          : lastName // ignore: cast_nullable_to_non_nullable
              as String?,
      siteName: freezed == siteName
          ? _value.siteName
          : siteName // ignore: cast_nullable_to_non_nullable
              as String?,
      siteUrl: freezed == siteUrl
          ? _value.siteUrl
          : siteUrl // ignore: cast_nullable_to_non_nullable
              as String?,
      socialNetwork: freezed == socialNetwork
          ? _value.socialNetwork
          : socialNetwork // ignore: cast_nullable_to_non_nullable
              as String?,
      profilePictureUrl: freezed == profilePictureUrl
          ? _value.profilePictureUrl
          : profilePictureUrl // ignore: cast_nullable_to_non_nullable
              as String?,
      tokenType: freezed == tokenType
          ? _value.tokenType
          : tokenType // ignore: cast_nullable_to_non_nullable
              as String?,
      totalFollowerLevel: null == totalFollowerLevel
          ? _value.totalFollowerLevel
          : totalFollowerLevel // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$SocialSignInPayloadImpl implements _SocialSignInPayload {
  const _$SocialSignInPayloadImpl(
      {required this.accessToken,
      required this.countryCode,
      required this.socialNetworkType,
      @JsonKey(includeIfNull: false) this.firstName,
      @JsonKey(includeIfNull: false) this.lastName,
      @JsonKey(includeIfNull: false) this.siteName,
      @JsonKey(includeIfNull: false) this.siteUrl,
      @JsonKey(includeIfNull: false) this.socialNetwork,
      @JsonKey(includeIfNull: false) this.profilePictureUrl,
      @JsonKey(includeIfNull: false) this.tokenType,
      this.totalFollowerLevel = 'EMPTY'});

  factory _$SocialSignInPayloadImpl.fromJson(Map<String, dynamic> json) =>
      _$$SocialSignInPayloadImplFromJson(json);

  @override
  final String accessToken;
  @override
  final String countryCode;
  @override
  final String socialNetworkType;
  @override
  @JsonKey(includeIfNull: false)
  final String? firstName;
  @override
  @JsonKey(includeIfNull: false)
  final String? lastName;
  @override
  @JsonKey(includeIfNull: false)
  final String? siteName;
  @override
  @JsonKey(includeIfNull: false)
  final String? siteUrl;
  @override
  @JsonKey(includeIfNull: false)
  final String? socialNetwork;
  @override
  @JsonKey(includeIfNull: false)
  final String? profilePictureUrl;
  @override
  @JsonKey(includeIfNull: false)
  final String? tokenType;
  @override
  @JsonKey()
  final String totalFollowerLevel;

  @override
  String toString() {
    return 'SocialSignInPayload(accessToken: $accessToken, countryCode: $countryCode, socialNetworkType: $socialNetworkType, firstName: $firstName, lastName: $lastName, siteName: $siteName, siteUrl: $siteUrl, socialNetwork: $socialNetwork, profilePictureUrl: $profilePictureUrl, tokenType: $tokenType, totalFollowerLevel: $totalFollowerLevel)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$SocialSignInPayloadImpl &&
            (identical(other.accessToken, accessToken) ||
                other.accessToken == accessToken) &&
            (identical(other.countryCode, countryCode) ||
                other.countryCode == countryCode) &&
            (identical(other.socialNetworkType, socialNetworkType) ||
                other.socialNetworkType == socialNetworkType) &&
            (identical(other.firstName, firstName) ||
                other.firstName == firstName) &&
            (identical(other.lastName, lastName) ||
                other.lastName == lastName) &&
            (identical(other.siteName, siteName) ||
                other.siteName == siteName) &&
            (identical(other.siteUrl, siteUrl) || other.siteUrl == siteUrl) &&
            (identical(other.socialNetwork, socialNetwork) ||
                other.socialNetwork == socialNetwork) &&
            (identical(other.profilePictureUrl, profilePictureUrl) ||
                other.profilePictureUrl == profilePictureUrl) &&
            (identical(other.tokenType, tokenType) ||
                other.tokenType == tokenType) &&
            (identical(other.totalFollowerLevel, totalFollowerLevel) ||
                other.totalFollowerLevel == totalFollowerLevel));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      accessToken,
      countryCode,
      socialNetworkType,
      firstName,
      lastName,
      siteName,
      siteUrl,
      socialNetwork,
      profilePictureUrl,
      tokenType,
      totalFollowerLevel);

  /// Create a copy of SocialSignInPayload
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$SocialSignInPayloadImplCopyWith<_$SocialSignInPayloadImpl> get copyWith =>
      __$$SocialSignInPayloadImplCopyWithImpl<_$SocialSignInPayloadImpl>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$SocialSignInPayloadImplToJson(
      this,
    );
  }
}

abstract class _SocialSignInPayload implements SocialSignInPayload {
  const factory _SocialSignInPayload(
      {required final String accessToken,
      required final String countryCode,
      required final String socialNetworkType,
      @JsonKey(includeIfNull: false) final String? firstName,
      @JsonKey(includeIfNull: false) final String? lastName,
      @JsonKey(includeIfNull: false) final String? siteName,
      @JsonKey(includeIfNull: false) final String? siteUrl,
      @JsonKey(includeIfNull: false) final String? socialNetwork,
      @JsonKey(includeIfNull: false) final String? profilePictureUrl,
      @JsonKey(includeIfNull: false) final String? tokenType,
      final String totalFollowerLevel}) = _$SocialSignInPayloadImpl;

  factory _SocialSignInPayload.fromJson(Map<String, dynamic> json) =
      _$SocialSignInPayloadImpl.fromJson;

  @override
  String get accessToken;
  @override
  String get countryCode;
  @override
  String get socialNetworkType;
  @override
  @JsonKey(includeIfNull: false)
  String? get firstName;
  @override
  @JsonKey(includeIfNull: false)
  String? get lastName;
  @override
  @JsonKey(includeIfNull: false)
  String? get siteName;
  @override
  @JsonKey(includeIfNull: false)
  String? get siteUrl;
  @override
  @JsonKey(includeIfNull: false)
  String? get socialNetwork;
  @override
  @JsonKey(includeIfNull: false)
  String? get profilePictureUrl;
  @override
  @JsonKey(includeIfNull: false)
  String? get tokenType;
  @override
  String get totalFollowerLevel;

  /// Create a copy of SocialSignInPayload
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$SocialSignInPayloadImplCopyWith<_$SocialSignInPayloadImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

SocialLoginResponse _$SocialLoginResponseFromJson(Map<String, dynamic> json) {
  return _SocialLoginResponse.fromJson(json);
}

/// @nodoc
mixin _$SocialLoginResponse {
  String get accessToken => throw _privateConstructorUsedError;
  String get refreshToken => throw _privateConstructorUsedError;
  String get tokenType => throw _privateConstructorUsedError;
  int get expiresIn => throw _privateConstructorUsedError;
  String? get scope => throw _privateConstructorUsedError;

  /// Serializes this SocialLoginResponse to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of SocialLoginResponse
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $SocialLoginResponseCopyWith<SocialLoginResponse> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $SocialLoginResponseCopyWith<$Res> {
  factory $SocialLoginResponseCopyWith(
          SocialLoginResponse value, $Res Function(SocialLoginResponse) then) =
      _$SocialLoginResponseCopyWithImpl<$Res, SocialLoginResponse>;
  @useResult
  $Res call(
      {String accessToken,
      String refreshToken,
      String tokenType,
      int expiresIn,
      String? scope});
}

/// @nodoc
class _$SocialLoginResponseCopyWithImpl<$Res, $Val extends SocialLoginResponse>
    implements $SocialLoginResponseCopyWith<$Res> {
  _$SocialLoginResponseCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of SocialLoginResponse
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? accessToken = null,
    Object? refreshToken = null,
    Object? tokenType = null,
    Object? expiresIn = null,
    Object? scope = freezed,
  }) {
    return _then(_value.copyWith(
      accessToken: null == accessToken
          ? _value.accessToken
          : accessToken // ignore: cast_nullable_to_non_nullable
              as String,
      refreshToken: null == refreshToken
          ? _value.refreshToken
          : refreshToken // ignore: cast_nullable_to_non_nullable
              as String,
      tokenType: null == tokenType
          ? _value.tokenType
          : tokenType // ignore: cast_nullable_to_non_nullable
              as String,
      expiresIn: null == expiresIn
          ? _value.expiresIn
          : expiresIn // ignore: cast_nullable_to_non_nullable
              as int,
      scope: freezed == scope
          ? _value.scope
          : scope // ignore: cast_nullable_to_non_nullable
              as String?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$SocialLoginResponseImplCopyWith<$Res>
    implements $SocialLoginResponseCopyWith<$Res> {
  factory _$$SocialLoginResponseImplCopyWith(_$SocialLoginResponseImpl value,
          $Res Function(_$SocialLoginResponseImpl) then) =
      __$$SocialLoginResponseImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {String accessToken,
      String refreshToken,
      String tokenType,
      int expiresIn,
      String? scope});
}

/// @nodoc
class __$$SocialLoginResponseImplCopyWithImpl<$Res>
    extends _$SocialLoginResponseCopyWithImpl<$Res, _$SocialLoginResponseImpl>
    implements _$$SocialLoginResponseImplCopyWith<$Res> {
  __$$SocialLoginResponseImplCopyWithImpl(_$SocialLoginResponseImpl _value,
      $Res Function(_$SocialLoginResponseImpl) _then)
      : super(_value, _then);

  /// Create a copy of SocialLoginResponse
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? accessToken = null,
    Object? refreshToken = null,
    Object? tokenType = null,
    Object? expiresIn = null,
    Object? scope = freezed,
  }) {
    return _then(_$SocialLoginResponseImpl(
      accessToken: null == accessToken
          ? _value.accessToken
          : accessToken // ignore: cast_nullable_to_non_nullable
              as String,
      refreshToken: null == refreshToken
          ? _value.refreshToken
          : refreshToken // ignore: cast_nullable_to_non_nullable
              as String,
      tokenType: null == tokenType
          ? _value.tokenType
          : tokenType // ignore: cast_nullable_to_non_nullable
              as String,
      expiresIn: null == expiresIn
          ? _value.expiresIn
          : expiresIn // ignore: cast_nullable_to_non_nullable
              as int,
      scope: freezed == scope
          ? _value.scope
          : scope // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$SocialLoginResponseImpl implements _SocialLoginResponse {
  const _$SocialLoginResponseImpl(
      {required this.accessToken,
      required this.refreshToken,
      required this.tokenType,
      required this.expiresIn,
      this.scope});

  factory _$SocialLoginResponseImpl.fromJson(Map<String, dynamic> json) =>
      _$$SocialLoginResponseImplFromJson(json);

  @override
  final String accessToken;
  @override
  final String refreshToken;
  @override
  final String tokenType;
  @override
  final int expiresIn;
  @override
  final String? scope;

  @override
  String toString() {
    return 'SocialLoginResponse(accessToken: $accessToken, refreshToken: $refreshToken, tokenType: $tokenType, expiresIn: $expiresIn, scope: $scope)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$SocialLoginResponseImpl &&
            (identical(other.accessToken, accessToken) ||
                other.accessToken == accessToken) &&
            (identical(other.refreshToken, refreshToken) ||
                other.refreshToken == refreshToken) &&
            (identical(other.tokenType, tokenType) ||
                other.tokenType == tokenType) &&
            (identical(other.expiresIn, expiresIn) ||
                other.expiresIn == expiresIn) &&
            (identical(other.scope, scope) || other.scope == scope));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
      runtimeType, accessToken, refreshToken, tokenType, expiresIn, scope);

  /// Create a copy of SocialLoginResponse
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$SocialLoginResponseImplCopyWith<_$SocialLoginResponseImpl> get copyWith =>
      __$$SocialLoginResponseImplCopyWithImpl<_$SocialLoginResponseImpl>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$SocialLoginResponseImplToJson(
      this,
    );
  }
}

abstract class _SocialLoginResponse implements SocialLoginResponse {
  const factory _SocialLoginResponse(
      {required final String accessToken,
      required final String refreshToken,
      required final String tokenType,
      required final int expiresIn,
      final String? scope}) = _$SocialLoginResponseImpl;

  factory _SocialLoginResponse.fromJson(Map<String, dynamic> json) =
      _$SocialLoginResponseImpl.fromJson;

  @override
  String get accessToken;
  @override
  String get refreshToken;
  @override
  String get tokenType;
  @override
  int get expiresIn;
  @override
  String? get scope;

  /// Create a copy of SocialLoginResponse
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$SocialLoginResponseImplCopyWith<_$SocialLoginResponseImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

GoogleSignInTokens _$GoogleSignInTokensFromJson(Map<String, dynamic> json) {
  return _GoogleSignInTokens.fromJson(json);
}

/// @nodoc
mixin _$GoogleSignInTokens {
  String? get accessToken => throw _privateConstructorUsedError;
  String? get idToken => throw _privateConstructorUsedError;
  String get email => throw _privateConstructorUsedError;
  String? get displayName => throw _privateConstructorUsedError;
  String? get photoUrl => throw _privateConstructorUsedError;

  /// Serializes this GoogleSignInTokens to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of GoogleSignInTokens
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $GoogleSignInTokensCopyWith<GoogleSignInTokens> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $GoogleSignInTokensCopyWith<$Res> {
  factory $GoogleSignInTokensCopyWith(
          GoogleSignInTokens value, $Res Function(GoogleSignInTokens) then) =
      _$GoogleSignInTokensCopyWithImpl<$Res, GoogleSignInTokens>;
  @useResult
  $Res call(
      {String? accessToken,
      String? idToken,
      String email,
      String? displayName,
      String? photoUrl});
}

/// @nodoc
class _$GoogleSignInTokensCopyWithImpl<$Res, $Val extends GoogleSignInTokens>
    implements $GoogleSignInTokensCopyWith<$Res> {
  _$GoogleSignInTokensCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of GoogleSignInTokens
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? accessToken = freezed,
    Object? idToken = freezed,
    Object? email = null,
    Object? displayName = freezed,
    Object? photoUrl = freezed,
  }) {
    return _then(_value.copyWith(
      accessToken: freezed == accessToken
          ? _value.accessToken
          : accessToken // ignore: cast_nullable_to_non_nullable
              as String?,
      idToken: freezed == idToken
          ? _value.idToken
          : idToken // ignore: cast_nullable_to_non_nullable
              as String?,
      email: null == email
          ? _value.email
          : email // ignore: cast_nullable_to_non_nullable
              as String,
      displayName: freezed == displayName
          ? _value.displayName
          : displayName // ignore: cast_nullable_to_non_nullable
              as String?,
      photoUrl: freezed == photoUrl
          ? _value.photoUrl
          : photoUrl // ignore: cast_nullable_to_non_nullable
              as String?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$GoogleSignInTokensImplCopyWith<$Res>
    implements $GoogleSignInTokensCopyWith<$Res> {
  factory _$$GoogleSignInTokensImplCopyWith(_$GoogleSignInTokensImpl value,
          $Res Function(_$GoogleSignInTokensImpl) then) =
      __$$GoogleSignInTokensImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {String? accessToken,
      String? idToken,
      String email,
      String? displayName,
      String? photoUrl});
}

/// @nodoc
class __$$GoogleSignInTokensImplCopyWithImpl<$Res>
    extends _$GoogleSignInTokensCopyWithImpl<$Res, _$GoogleSignInTokensImpl>
    implements _$$GoogleSignInTokensImplCopyWith<$Res> {
  __$$GoogleSignInTokensImplCopyWithImpl(_$GoogleSignInTokensImpl _value,
      $Res Function(_$GoogleSignInTokensImpl) _then)
      : super(_value, _then);

  /// Create a copy of GoogleSignInTokens
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? accessToken = freezed,
    Object? idToken = freezed,
    Object? email = null,
    Object? displayName = freezed,
    Object? photoUrl = freezed,
  }) {
    return _then(_$GoogleSignInTokensImpl(
      accessToken: freezed == accessToken
          ? _value.accessToken
          : accessToken // ignore: cast_nullable_to_non_nullable
              as String?,
      idToken: freezed == idToken
          ? _value.idToken
          : idToken // ignore: cast_nullable_to_non_nullable
              as String?,
      email: null == email
          ? _value.email
          : email // ignore: cast_nullable_to_non_nullable
              as String,
      displayName: freezed == displayName
          ? _value.displayName
          : displayName // ignore: cast_nullable_to_non_nullable
              as String?,
      photoUrl: freezed == photoUrl
          ? _value.photoUrl
          : photoUrl // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$GoogleSignInTokensImpl implements _GoogleSignInTokens {
  const _$GoogleSignInTokensImpl(
      {this.accessToken,
      this.idToken,
      required this.email,
      this.displayName,
      this.photoUrl});

  factory _$GoogleSignInTokensImpl.fromJson(Map<String, dynamic> json) =>
      _$$GoogleSignInTokensImplFromJson(json);

  @override
  final String? accessToken;
  @override
  final String? idToken;
  @override
  final String email;
  @override
  final String? displayName;
  @override
  final String? photoUrl;

  @override
  String toString() {
    return 'GoogleSignInTokens(accessToken: $accessToken, idToken: $idToken, email: $email, displayName: $displayName, photoUrl: $photoUrl)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$GoogleSignInTokensImpl &&
            (identical(other.accessToken, accessToken) ||
                other.accessToken == accessToken) &&
            (identical(other.idToken, idToken) || other.idToken == idToken) &&
            (identical(other.email, email) || other.email == email) &&
            (identical(other.displayName, displayName) ||
                other.displayName == displayName) &&
            (identical(other.photoUrl, photoUrl) ||
                other.photoUrl == photoUrl));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
      runtimeType, accessToken, idToken, email, displayName, photoUrl);

  /// Create a copy of GoogleSignInTokens
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$GoogleSignInTokensImplCopyWith<_$GoogleSignInTokensImpl> get copyWith =>
      __$$GoogleSignInTokensImplCopyWithImpl<_$GoogleSignInTokensImpl>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$GoogleSignInTokensImplToJson(
      this,
    );
  }
}

abstract class _GoogleSignInTokens implements GoogleSignInTokens {
  const factory _GoogleSignInTokens(
      {final String? accessToken,
      final String? idToken,
      required final String email,
      final String? displayName,
      final String? photoUrl}) = _$GoogleSignInTokensImpl;

  factory _GoogleSignInTokens.fromJson(Map<String, dynamic> json) =
      _$GoogleSignInTokensImpl.fromJson;

  @override
  String? get accessToken;
  @override
  String? get idToken;
  @override
  String get email;
  @override
  String? get displayName;
  @override
  String? get photoUrl;

  /// Create a copy of GoogleSignInTokens
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$GoogleSignInTokensImplCopyWith<_$GoogleSignInTokensImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

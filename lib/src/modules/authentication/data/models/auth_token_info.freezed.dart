// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'auth_token_info.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

AuthTokenInfo _$AuthTokenInfoFromJson(Map<String, dynamic> json) {
  return _AuthTokenInfo.fromJson(json);
}

/// @nodoc
mixin _$AuthTokenInfo {
  String get token => throw _privateConstructorUsedError;
  int get expiresIn => throw _privateConstructorUsedError;
  int get refreshExpiresIn => throw _privateConstructorUsedError;
  String get refreshToken => throw _privateConstructorUsedError;
  String? get tokenType => throw _privateConstructorUsedError;
  int? get notBeforePolicy => throw _privateConstructorUsedError;
  String? get sessionState => throw _privateConstructorUsedError;
  String? get scope => throw _privateConstructorUsedError;

  /// Serializes this AuthTokenInfo to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of AuthTokenInfo
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $AuthTokenInfoCopyWith<AuthTokenInfo> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $AuthTokenInfoCopyWith<$Res> {
  factory $AuthTokenInfoCopyWith(
          AuthTokenInfo value, $Res Function(AuthTokenInfo) then) =
      _$AuthTokenInfoCopyWithImpl<$Res, AuthTokenInfo>;
  @useResult
  $Res call(
      {String token,
      int expiresIn,
      int refreshExpiresIn,
      String refreshToken,
      String? tokenType,
      int? notBeforePolicy,
      String? sessionState,
      String? scope});
}

/// @nodoc
class _$AuthTokenInfoCopyWithImpl<$Res, $Val extends AuthTokenInfo>
    implements $AuthTokenInfoCopyWith<$Res> {
  _$AuthTokenInfoCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of AuthTokenInfo
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? token = null,
    Object? expiresIn = null,
    Object? refreshExpiresIn = null,
    Object? refreshToken = null,
    Object? tokenType = freezed,
    Object? notBeforePolicy = freezed,
    Object? sessionState = freezed,
    Object? scope = freezed,
  }) {
    return _then(_value.copyWith(
      token: null == token
          ? _value.token
          : token // ignore: cast_nullable_to_non_nullable
              as String,
      expiresIn: null == expiresIn
          ? _value.expiresIn
          : expiresIn // ignore: cast_nullable_to_non_nullable
              as int,
      refreshExpiresIn: null == refreshExpiresIn
          ? _value.refreshExpiresIn
          : refreshExpiresIn // ignore: cast_nullable_to_non_nullable
              as int,
      refreshToken: null == refreshToken
          ? _value.refreshToken
          : refreshToken // ignore: cast_nullable_to_non_nullable
              as String,
      tokenType: freezed == tokenType
          ? _value.tokenType
          : tokenType // ignore: cast_nullable_to_non_nullable
              as String?,
      notBeforePolicy: freezed == notBeforePolicy
          ? _value.notBeforePolicy
          : notBeforePolicy // ignore: cast_nullable_to_non_nullable
              as int?,
      sessionState: freezed == sessionState
          ? _value.sessionState
          : sessionState // ignore: cast_nullable_to_non_nullable
              as String?,
      scope: freezed == scope
          ? _value.scope
          : scope // ignore: cast_nullable_to_non_nullable
              as String?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$AuthTokenInfoImplCopyWith<$Res>
    implements $AuthTokenInfoCopyWith<$Res> {
  factory _$$AuthTokenInfoImplCopyWith(
          _$AuthTokenInfoImpl value, $Res Function(_$AuthTokenInfoImpl) then) =
      __$$AuthTokenInfoImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {String token,
      int expiresIn,
      int refreshExpiresIn,
      String refreshToken,
      String? tokenType,
      int? notBeforePolicy,
      String? sessionState,
      String? scope});
}

/// @nodoc
class __$$AuthTokenInfoImplCopyWithImpl<$Res>
    extends _$AuthTokenInfoCopyWithImpl<$Res, _$AuthTokenInfoImpl>
    implements _$$AuthTokenInfoImplCopyWith<$Res> {
  __$$AuthTokenInfoImplCopyWithImpl(
      _$AuthTokenInfoImpl _value, $Res Function(_$AuthTokenInfoImpl) _then)
      : super(_value, _then);

  /// Create a copy of AuthTokenInfo
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? token = null,
    Object? expiresIn = null,
    Object? refreshExpiresIn = null,
    Object? refreshToken = null,
    Object? tokenType = freezed,
    Object? notBeforePolicy = freezed,
    Object? sessionState = freezed,
    Object? scope = freezed,
  }) {
    return _then(_$AuthTokenInfoImpl(
      token: null == token
          ? _value.token
          : token // ignore: cast_nullable_to_non_nullable
              as String,
      expiresIn: null == expiresIn
          ? _value.expiresIn
          : expiresIn // ignore: cast_nullable_to_non_nullable
              as int,
      refreshExpiresIn: null == refreshExpiresIn
          ? _value.refreshExpiresIn
          : refreshExpiresIn // ignore: cast_nullable_to_non_nullable
              as int,
      refreshToken: null == refreshToken
          ? _value.refreshToken
          : refreshToken // ignore: cast_nullable_to_non_nullable
              as String,
      tokenType: freezed == tokenType
          ? _value.tokenType
          : tokenType // ignore: cast_nullable_to_non_nullable
              as String?,
      notBeforePolicy: freezed == notBeforePolicy
          ? _value.notBeforePolicy
          : notBeforePolicy // ignore: cast_nullable_to_non_nullable
              as int?,
      sessionState: freezed == sessionState
          ? _value.sessionState
          : sessionState // ignore: cast_nullable_to_non_nullable
              as String?,
      scope: freezed == scope
          ? _value.scope
          : scope // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$AuthTokenInfoImpl implements _AuthTokenInfo {
  const _$AuthTokenInfoImpl(
      {required this.token,
      required this.expiresIn,
      required this.refreshExpiresIn,
      required this.refreshToken,
      this.tokenType,
      this.notBeforePolicy,
      this.sessionState,
      this.scope});

  factory _$AuthTokenInfoImpl.fromJson(Map<String, dynamic> json) =>
      _$$AuthTokenInfoImplFromJson(json);

  @override
  final String token;
  @override
  final int expiresIn;
  @override
  final int refreshExpiresIn;
  @override
  final String refreshToken;
  @override
  final String? tokenType;
  @override
  final int? notBeforePolicy;
  @override
  final String? sessionState;
  @override
  final String? scope;

  @override
  String toString() {
    return 'AuthTokenInfo(token: $token, expiresIn: $expiresIn, refreshExpiresIn: $refreshExpiresIn, refreshToken: $refreshToken, tokenType: $tokenType, notBeforePolicy: $notBeforePolicy, sessionState: $sessionState, scope: $scope)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$AuthTokenInfoImpl &&
            (identical(other.token, token) || other.token == token) &&
            (identical(other.expiresIn, expiresIn) ||
                other.expiresIn == expiresIn) &&
            (identical(other.refreshExpiresIn, refreshExpiresIn) ||
                other.refreshExpiresIn == refreshExpiresIn) &&
            (identical(other.refreshToken, refreshToken) ||
                other.refreshToken == refreshToken) &&
            (identical(other.tokenType, tokenType) ||
                other.tokenType == tokenType) &&
            (identical(other.notBeforePolicy, notBeforePolicy) ||
                other.notBeforePolicy == notBeforePolicy) &&
            (identical(other.sessionState, sessionState) ||
                other.sessionState == sessionState) &&
            (identical(other.scope, scope) || other.scope == scope));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      token,
      expiresIn,
      refreshExpiresIn,
      refreshToken,
      tokenType,
      notBeforePolicy,
      sessionState,
      scope);

  /// Create a copy of AuthTokenInfo
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$AuthTokenInfoImplCopyWith<_$AuthTokenInfoImpl> get copyWith =>
      __$$AuthTokenInfoImplCopyWithImpl<_$AuthTokenInfoImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$AuthTokenInfoImplToJson(
      this,
    );
  }
}

abstract class _AuthTokenInfo implements AuthTokenInfo {
  const factory _AuthTokenInfo(
      {required final String token,
      required final int expiresIn,
      required final int refreshExpiresIn,
      required final String refreshToken,
      final String? tokenType,
      final int? notBeforePolicy,
      final String? sessionState,
      final String? scope}) = _$AuthTokenInfoImpl;

  factory _AuthTokenInfo.fromJson(Map<String, dynamic> json) =
      _$AuthTokenInfoImpl.fromJson;

  @override
  String get token;
  @override
  int get expiresIn;
  @override
  int get refreshExpiresIn;
  @override
  String get refreshToken;
  @override
  String? get tokenType;
  @override
  int? get notBeforePolicy;
  @override
  String? get sessionState;
  @override
  String? get scope;

  /// Create a copy of AuthTokenInfo
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$AuthTokenInfoImplCopyWith<_$AuthTokenInfoImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

RefreshTokenRequest _$RefreshTokenRequestFromJson(Map<String, dynamic> json) {
  return _RefreshTokenRequest.fromJson(json);
}

/// @nodoc
mixin _$RefreshTokenRequest {
  String get refreshToken => throw _privateConstructorUsedError;
  String get clientId => throw _privateConstructorUsedError;

  /// Serializes this RefreshTokenRequest to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of RefreshTokenRequest
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $RefreshTokenRequestCopyWith<RefreshTokenRequest> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $RefreshTokenRequestCopyWith<$Res> {
  factory $RefreshTokenRequestCopyWith(
          RefreshTokenRequest value, $Res Function(RefreshTokenRequest) then) =
      _$RefreshTokenRequestCopyWithImpl<$Res, RefreshTokenRequest>;
  @useResult
  $Res call({String refreshToken, String clientId});
}

/// @nodoc
class _$RefreshTokenRequestCopyWithImpl<$Res, $Val extends RefreshTokenRequest>
    implements $RefreshTokenRequestCopyWith<$Res> {
  _$RefreshTokenRequestCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of RefreshTokenRequest
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? refreshToken = null,
    Object? clientId = null,
  }) {
    return _then(_value.copyWith(
      refreshToken: null == refreshToken
          ? _value.refreshToken
          : refreshToken // ignore: cast_nullable_to_non_nullable
              as String,
      clientId: null == clientId
          ? _value.clientId
          : clientId // ignore: cast_nullable_to_non_nullable
              as String,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$RefreshTokenRequestImplCopyWith<$Res>
    implements $RefreshTokenRequestCopyWith<$Res> {
  factory _$$RefreshTokenRequestImplCopyWith(_$RefreshTokenRequestImpl value,
          $Res Function(_$RefreshTokenRequestImpl) then) =
      __$$RefreshTokenRequestImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({String refreshToken, String clientId});
}

/// @nodoc
class __$$RefreshTokenRequestImplCopyWithImpl<$Res>
    extends _$RefreshTokenRequestCopyWithImpl<$Res, _$RefreshTokenRequestImpl>
    implements _$$RefreshTokenRequestImplCopyWith<$Res> {
  __$$RefreshTokenRequestImplCopyWithImpl(_$RefreshTokenRequestImpl _value,
      $Res Function(_$RefreshTokenRequestImpl) _then)
      : super(_value, _then);

  /// Create a copy of RefreshTokenRequest
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? refreshToken = null,
    Object? clientId = null,
  }) {
    return _then(_$RefreshTokenRequestImpl(
      refreshToken: null == refreshToken
          ? _value.refreshToken
          : refreshToken // ignore: cast_nullable_to_non_nullable
              as String,
      clientId: null == clientId
          ? _value.clientId
          : clientId // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$RefreshTokenRequestImpl implements _RefreshTokenRequest {
  _$RefreshTokenRequestImpl(
      {required this.refreshToken, required this.clientId});

  factory _$RefreshTokenRequestImpl.fromJson(Map<String, dynamic> json) =>
      _$$RefreshTokenRequestImplFromJson(json);

  @override
  final String refreshToken;
  @override
  final String clientId;

  @override
  String toString() {
    return 'RefreshTokenRequest(refreshToken: $refreshToken, clientId: $clientId)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$RefreshTokenRequestImpl &&
            (identical(other.refreshToken, refreshToken) ||
                other.refreshToken == refreshToken) &&
            (identical(other.clientId, clientId) ||
                other.clientId == clientId));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, refreshToken, clientId);

  /// Create a copy of RefreshTokenRequest
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$RefreshTokenRequestImplCopyWith<_$RefreshTokenRequestImpl> get copyWith =>
      __$$RefreshTokenRequestImplCopyWithImpl<_$RefreshTokenRequestImpl>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$RefreshTokenRequestImplToJson(
      this,
    );
  }
}

abstract class _RefreshTokenRequest implements RefreshTokenRequest {
  factory _RefreshTokenRequest(
      {required final String refreshToken,
      required final String clientId}) = _$RefreshTokenRequestImpl;

  factory _RefreshTokenRequest.fromJson(Map<String, dynamic> json) =
      _$RefreshTokenRequestImpl.fromJson;

  @override
  String get refreshToken;
  @override
  String get clientId;

  /// Create a copy of RefreshTokenRequest
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$RefreshTokenRequestImplCopyWith<_$RefreshTokenRequestImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

FacebookAuthResponse _$FacebookAuthResponseFromJson(Map<String, dynamic> json) {
  return _FacebookAuthResponse.fromJson(json);
}

/// @nodoc
mixin _$FacebookAuthResponse {
  String get id => throw _privateConstructorUsedError;
  String? get first_name => throw _privateConstructorUsedError;
  String? get last_name => throw _privateConstructorUsedError;
  String? get name => throw _privateConstructorUsedError;
  String? get email => throw _privateConstructorUsedError;
  FacebookPicture? get picture => throw _privateConstructorUsedError;
  String? get accessToken => throw _privateConstructorUsedError;

  /// Serializes this FacebookAuthResponse to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of FacebookAuthResponse
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $FacebookAuthResponseCopyWith<FacebookAuthResponse> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $FacebookAuthResponseCopyWith<$Res> {
  factory $FacebookAuthResponseCopyWith(FacebookAuthResponse value,
          $Res Function(FacebookAuthResponse) then) =
      _$FacebookAuthResponseCopyWithImpl<$Res, FacebookAuthResponse>;
  @useResult
  $Res call(
      {String id,
      String? first_name,
      String? last_name,
      String? name,
      String? email,
      FacebookPicture? picture,
      String? accessToken});

  $FacebookPictureCopyWith<$Res>? get picture;
}

/// @nodoc
class _$FacebookAuthResponseCopyWithImpl<$Res,
        $Val extends FacebookAuthResponse>
    implements $FacebookAuthResponseCopyWith<$Res> {
  _$FacebookAuthResponseCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of FacebookAuthResponse
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? first_name = freezed,
    Object? last_name = freezed,
    Object? name = freezed,
    Object? email = freezed,
    Object? picture = freezed,
    Object? accessToken = freezed,
  }) {
    return _then(_value.copyWith(
      id: null == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as String,
      first_name: freezed == first_name
          ? _value.first_name
          : first_name // ignore: cast_nullable_to_non_nullable
              as String?,
      last_name: freezed == last_name
          ? _value.last_name
          : last_name // ignore: cast_nullable_to_non_nullable
              as String?,
      name: freezed == name
          ? _value.name
          : name // ignore: cast_nullable_to_non_nullable
              as String?,
      email: freezed == email
          ? _value.email
          : email // ignore: cast_nullable_to_non_nullable
              as String?,
      picture: freezed == picture
          ? _value.picture
          : picture // ignore: cast_nullable_to_non_nullable
              as FacebookPicture?,
      accessToken: freezed == accessToken
          ? _value.accessToken
          : accessToken // ignore: cast_nullable_to_non_nullable
              as String?,
    ) as $Val);
  }

  /// Create a copy of FacebookAuthResponse
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $FacebookPictureCopyWith<$Res>? get picture {
    if (_value.picture == null) {
      return null;
    }

    return $FacebookPictureCopyWith<$Res>(_value.picture!, (value) {
      return _then(_value.copyWith(picture: value) as $Val);
    });
  }
}

/// @nodoc
abstract class _$$FacebookAuthResponseImplCopyWith<$Res>
    implements $FacebookAuthResponseCopyWith<$Res> {
  factory _$$FacebookAuthResponseImplCopyWith(_$FacebookAuthResponseImpl value,
          $Res Function(_$FacebookAuthResponseImpl) then) =
      __$$FacebookAuthResponseImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {String id,
      String? first_name,
      String? last_name,
      String? name,
      String? email,
      FacebookPicture? picture,
      String? accessToken});

  @override
  $FacebookPictureCopyWith<$Res>? get picture;
}

/// @nodoc
class __$$FacebookAuthResponseImplCopyWithImpl<$Res>
    extends _$FacebookAuthResponseCopyWithImpl<$Res, _$FacebookAuthResponseImpl>
    implements _$$FacebookAuthResponseImplCopyWith<$Res> {
  __$$FacebookAuthResponseImplCopyWithImpl(_$FacebookAuthResponseImpl _value,
      $Res Function(_$FacebookAuthResponseImpl) _then)
      : super(_value, _then);

  /// Create a copy of FacebookAuthResponse
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? first_name = freezed,
    Object? last_name = freezed,
    Object? name = freezed,
    Object? email = freezed,
    Object? picture = freezed,
    Object? accessToken = freezed,
  }) {
    return _then(_$FacebookAuthResponseImpl(
      id: null == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as String,
      first_name: freezed == first_name
          ? _value.first_name
          : first_name // ignore: cast_nullable_to_non_nullable
              as String?,
      last_name: freezed == last_name
          ? _value.last_name
          : last_name // ignore: cast_nullable_to_non_nullable
              as String?,
      name: freezed == name
          ? _value.name
          : name // ignore: cast_nullable_to_non_nullable
              as String?,
      email: freezed == email
          ? _value.email
          : email // ignore: cast_nullable_to_non_nullable
              as String?,
      picture: freezed == picture
          ? _value.picture
          : picture // ignore: cast_nullable_to_non_nullable
              as FacebookPicture?,
      accessToken: freezed == accessToken
          ? _value.accessToken
          : accessToken // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$FacebookAuthResponseImpl implements _FacebookAuthResponse {
  const _$FacebookAuthResponseImpl(
      {required this.id,
      this.first_name,
      this.last_name,
      this.name,
      this.email,
      this.picture,
      this.accessToken});

  factory _$FacebookAuthResponseImpl.fromJson(Map<String, dynamic> json) =>
      _$$FacebookAuthResponseImplFromJson(json);

  @override
  final String id;
  @override
  final String? first_name;
  @override
  final String? last_name;
  @override
  final String? name;
  @override
  final String? email;
  @override
  final FacebookPicture? picture;
  @override
  final String? accessToken;

  @override
  String toString() {
    return 'FacebookAuthResponse(id: $id, first_name: $first_name, last_name: $last_name, name: $name, email: $email, picture: $picture, accessToken: $accessToken)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$FacebookAuthResponseImpl &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.first_name, first_name) ||
                other.first_name == first_name) &&
            (identical(other.last_name, last_name) ||
                other.last_name == last_name) &&
            (identical(other.name, name) || other.name == name) &&
            (identical(other.email, email) || other.email == email) &&
            (identical(other.picture, picture) || other.picture == picture) &&
            (identical(other.accessToken, accessToken) ||
                other.accessToken == accessToken));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, id, first_name, last_name, name,
      email, picture, accessToken);

  /// Create a copy of FacebookAuthResponse
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$FacebookAuthResponseImplCopyWith<_$FacebookAuthResponseImpl>
      get copyWith =>
          __$$FacebookAuthResponseImplCopyWithImpl<_$FacebookAuthResponseImpl>(
              this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$FacebookAuthResponseImplToJson(
      this,
    );
  }
}

abstract class _FacebookAuthResponse implements FacebookAuthResponse {
  const factory _FacebookAuthResponse(
      {required final String id,
      final String? first_name,
      final String? last_name,
      final String? name,
      final String? email,
      final FacebookPicture? picture,
      final String? accessToken}) = _$FacebookAuthResponseImpl;

  factory _FacebookAuthResponse.fromJson(Map<String, dynamic> json) =
      _$FacebookAuthResponseImpl.fromJson;

  @override
  String get id;
  @override
  String? get first_name;
  @override
  String? get last_name;
  @override
  String? get name;
  @override
  String? get email;
  @override
  FacebookPicture? get picture;
  @override
  String? get accessToken;

  /// Create a copy of FacebookAuthResponse
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$FacebookAuthResponseImplCopyWith<_$FacebookAuthResponseImpl>
      get copyWith => throw _privateConstructorUsedError;
}

FacebookPicture _$FacebookPictureFromJson(Map<String, dynamic> json) {
  return _FacebookPicture.fromJson(json);
}

/// @nodoc
mixin _$FacebookPicture {
  FacebookPictureData get data => throw _privateConstructorUsedError;

  /// Serializes this FacebookPicture to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of FacebookPicture
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $FacebookPictureCopyWith<FacebookPicture> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $FacebookPictureCopyWith<$Res> {
  factory $FacebookPictureCopyWith(
          FacebookPicture value, $Res Function(FacebookPicture) then) =
      _$FacebookPictureCopyWithImpl<$Res, FacebookPicture>;
  @useResult
  $Res call({FacebookPictureData data});

  $FacebookPictureDataCopyWith<$Res> get data;
}

/// @nodoc
class _$FacebookPictureCopyWithImpl<$Res, $Val extends FacebookPicture>
    implements $FacebookPictureCopyWith<$Res> {
  _$FacebookPictureCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of FacebookPicture
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? data = null,
  }) {
    return _then(_value.copyWith(
      data: null == data
          ? _value.data
          : data // ignore: cast_nullable_to_non_nullable
              as FacebookPictureData,
    ) as $Val);
  }

  /// Create a copy of FacebookPicture
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $FacebookPictureDataCopyWith<$Res> get data {
    return $FacebookPictureDataCopyWith<$Res>(_value.data, (value) {
      return _then(_value.copyWith(data: value) as $Val);
    });
  }
}

/// @nodoc
abstract class _$$FacebookPictureImplCopyWith<$Res>
    implements $FacebookPictureCopyWith<$Res> {
  factory _$$FacebookPictureImplCopyWith(_$FacebookPictureImpl value,
          $Res Function(_$FacebookPictureImpl) then) =
      __$$FacebookPictureImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({FacebookPictureData data});

  @override
  $FacebookPictureDataCopyWith<$Res> get data;
}

/// @nodoc
class __$$FacebookPictureImplCopyWithImpl<$Res>
    extends _$FacebookPictureCopyWithImpl<$Res, _$FacebookPictureImpl>
    implements _$$FacebookPictureImplCopyWith<$Res> {
  __$$FacebookPictureImplCopyWithImpl(
      _$FacebookPictureImpl _value, $Res Function(_$FacebookPictureImpl) _then)
      : super(_value, _then);

  /// Create a copy of FacebookPicture
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? data = null,
  }) {
    return _then(_$FacebookPictureImpl(
      data: null == data
          ? _value.data
          : data // ignore: cast_nullable_to_non_nullable
              as FacebookPictureData,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$FacebookPictureImpl implements _FacebookPicture {
  const _$FacebookPictureImpl({required this.data});

  factory _$FacebookPictureImpl.fromJson(Map<String, dynamic> json) =>
      _$$FacebookPictureImplFromJson(json);

  @override
  final FacebookPictureData data;

  @override
  String toString() {
    return 'FacebookPicture(data: $data)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$FacebookPictureImpl &&
            (identical(other.data, data) || other.data == data));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, data);

  /// Create a copy of FacebookPicture
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$FacebookPictureImplCopyWith<_$FacebookPictureImpl> get copyWith =>
      __$$FacebookPictureImplCopyWithImpl<_$FacebookPictureImpl>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$FacebookPictureImplToJson(
      this,
    );
  }
}

abstract class _FacebookPicture implements FacebookPicture {
  const factory _FacebookPicture({required final FacebookPictureData data}) =
      _$FacebookPictureImpl;

  factory _FacebookPicture.fromJson(Map<String, dynamic> json) =
      _$FacebookPictureImpl.fromJson;

  @override
  FacebookPictureData get data;

  /// Create a copy of FacebookPicture
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$FacebookPictureImplCopyWith<_$FacebookPictureImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

FacebookPictureData _$FacebookPictureDataFromJson(Map<String, dynamic> json) {
  return _FacebookPictureData.fromJson(json);
}

/// @nodoc
mixin _$FacebookPictureData {
  int get height => throw _privateConstructorUsedError;
  bool get is_silhouette => throw _privateConstructorUsedError;
  String get url => throw _privateConstructorUsedError;
  int get width => throw _privateConstructorUsedError;

  /// Serializes this FacebookPictureData to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of FacebookPictureData
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $FacebookPictureDataCopyWith<FacebookPictureData> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $FacebookPictureDataCopyWith<$Res> {
  factory $FacebookPictureDataCopyWith(
          FacebookPictureData value, $Res Function(FacebookPictureData) then) =
      _$FacebookPictureDataCopyWithImpl<$Res, FacebookPictureData>;
  @useResult
  $Res call({int height, bool is_silhouette, String url, int width});
}

/// @nodoc
class _$FacebookPictureDataCopyWithImpl<$Res, $Val extends FacebookPictureData>
    implements $FacebookPictureDataCopyWith<$Res> {
  _$FacebookPictureDataCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of FacebookPictureData
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? height = null,
    Object? is_silhouette = null,
    Object? url = null,
    Object? width = null,
  }) {
    return _then(_value.copyWith(
      height: null == height
          ? _value.height
          : height // ignore: cast_nullable_to_non_nullable
              as int,
      is_silhouette: null == is_silhouette
          ? _value.is_silhouette
          : is_silhouette // ignore: cast_nullable_to_non_nullable
              as bool,
      url: null == url
          ? _value.url
          : url // ignore: cast_nullable_to_non_nullable
              as String,
      width: null == width
          ? _value.width
          : width // ignore: cast_nullable_to_non_nullable
              as int,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$FacebookPictureDataImplCopyWith<$Res>
    implements $FacebookPictureDataCopyWith<$Res> {
  factory _$$FacebookPictureDataImplCopyWith(_$FacebookPictureDataImpl value,
          $Res Function(_$FacebookPictureDataImpl) then) =
      __$$FacebookPictureDataImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({int height, bool is_silhouette, String url, int width});
}

/// @nodoc
class __$$FacebookPictureDataImplCopyWithImpl<$Res>
    extends _$FacebookPictureDataCopyWithImpl<$Res, _$FacebookPictureDataImpl>
    implements _$$FacebookPictureDataImplCopyWith<$Res> {
  __$$FacebookPictureDataImplCopyWithImpl(_$FacebookPictureDataImpl _value,
      $Res Function(_$FacebookPictureDataImpl) _then)
      : super(_value, _then);

  /// Create a copy of FacebookPictureData
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? height = null,
    Object? is_silhouette = null,
    Object? url = null,
    Object? width = null,
  }) {
    return _then(_$FacebookPictureDataImpl(
      height: null == height
          ? _value.height
          : height // ignore: cast_nullable_to_non_nullable
              as int,
      is_silhouette: null == is_silhouette
          ? _value.is_silhouette
          : is_silhouette // ignore: cast_nullable_to_non_nullable
              as bool,
      url: null == url
          ? _value.url
          : url // ignore: cast_nullable_to_non_nullable
              as String,
      width: null == width
          ? _value.width
          : width // ignore: cast_nullable_to_non_nullable
              as int,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$FacebookPictureDataImpl implements _FacebookPictureData {
  const _$FacebookPictureDataImpl(
      {required this.height,
      required this.is_silhouette,
      required this.url,
      required this.width});

  factory _$FacebookPictureDataImpl.fromJson(Map<String, dynamic> json) =>
      _$$FacebookPictureDataImplFromJson(json);

  @override
  final int height;
  @override
  final bool is_silhouette;
  @override
  final String url;
  @override
  final int width;

  @override
  String toString() {
    return 'FacebookPictureData(height: $height, is_silhouette: $is_silhouette, url: $url, width: $width)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$FacebookPictureDataImpl &&
            (identical(other.height, height) || other.height == height) &&
            (identical(other.is_silhouette, is_silhouette) ||
                other.is_silhouette == is_silhouette) &&
            (identical(other.url, url) || other.url == url) &&
            (identical(other.width, width) || other.width == width));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode =>
      Object.hash(runtimeType, height, is_silhouette, url, width);

  /// Create a copy of FacebookPictureData
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$FacebookPictureDataImplCopyWith<_$FacebookPictureDataImpl> get copyWith =>
      __$$FacebookPictureDataImplCopyWithImpl<_$FacebookPictureDataImpl>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$FacebookPictureDataImplToJson(
      this,
    );
  }
}

abstract class _FacebookPictureData implements FacebookPictureData {
  const factory _FacebookPictureData(
      {required final int height,
      required final bool is_silhouette,
      required final String url,
      required final int width}) = _$FacebookPictureDataImpl;

  factory _FacebookPictureData.fromJson(Map<String, dynamic> json) =
      _$FacebookPictureDataImpl.fromJson;

  @override
  int get height;
  @override
  bool get is_silhouette;
  @override
  String get url;
  @override
  int get width;

  /// Create a copy of FacebookPictureData
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$FacebookPictureDataImplCopyWith<_$FacebookPictureDataImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

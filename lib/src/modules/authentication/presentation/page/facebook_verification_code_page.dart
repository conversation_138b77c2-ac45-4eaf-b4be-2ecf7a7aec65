import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_modular/flutter_modular.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:koc_app/src/base/base_page_state.dart';
import 'package:koc_app/src/modules/authentication/cubit/authentication_cubit.dart';
import 'package:koc_app/src/modules/authentication/presentation/widget/terms_and_privacy_widget.dart';
import 'package:koc_app/src/modules/shared/cubit/otp_timer_cubit.dart';
import 'package:koc_app/src/modules/shared/widget/otp_timer_field.dart';
import 'package:koc_app/src/shared/extensions.dart';
import 'package:koc_app/src/shared/services/shared_preferences_service.dart';

import '../../../../shared/constants/instance_key.dart';

class FacebookVerificationCodePage extends StatefulWidget {
  final String email;
  final bool isRegistered;

  const FacebookVerificationCodePage({
    super.key,
    required this.email,
    this.isRegistered = false,
  });

  @override
  State<FacebookVerificationCodePage> createState() => _FacebookVerificationCodePageState();
}

class _FacebookVerificationCodePageState extends BasePageState<FacebookVerificationCodePage, AuthenticationCubit> {
  late final OtpTimerCubit _otpTimerCubit;

  @override
  void initState() {
    super.initState();
    _otpTimerCubit = Modular.get<OtpTimerCubit>();
  }

  @override
  Widget buildPage(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        backgroundColor: Colors.transparent,
        elevation: 0,
        leading: IconButton(
          icon: const Icon(Icons.arrow_back, color: Colors.black),
          onPressed: () => Navigator.of(context).pop(),
        ),
      ),
      body: BlocProvider.value(
        value: _otpTimerCubit,
        child: _buildBody(),
      ),
    );
  }

  Widget _buildBody() {
    return Padding(
      padding: EdgeInsets.symmetric(horizontal: 24.r),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(height: 20.r),
          _buildTitle(),
          SizedBox(height: 40.r),
          Expanded(child: _buildOtpTimerField()),
        ],
      ),
    );
  }

  Widget _buildTitle() {
    return Text(
      'Continue with Facebook',
      style: context.textBodyLarge(fontWeight: FontWeight.w700),
      textAlign: TextAlign.left,
    );
  }

  Widget _buildOtpTimerField() {
    return OtpTimerField(
      verifyTarget: widget.email,
      btnName: widget.isRegistered ? 'Sign in' : 'Sign up',
      content: const TermsAndPrivacyWidget(),
      showLoading: () => commonCubit.showLoading(),
      hideLoading: () => commonCubit.hideLoading(),
      onTap: (otp) async {
        commonCubit.showLoading();

        try {
          final success = await cubit.handleFacebookVerifyOtp(otp);
          final sharedPreferencesService = Modular.get<SharedPreferencesService>();
          final isEmailRegistered =
              await sharedPreferencesService.getBoolFromInstance(InstanceConstants.isEmailRegisteredKey);

          if (success) {
            if (isEmailRegistered!) {
              if (mounted) {
                Modular.to.navigate("/navigation");
              }
            } else {
              if (mounted) {
                Modular.to.pushNamedAndRemoveUntil('/survey/user', (route) => false);
              }
            }
          } else if (mounted) {
            context
                .showSnackBar(cubit.state.errorMessage.isNotEmpty ? cubit.state.errorMessage : 'Verification failed');
          }
        } catch (e) {
          if (mounted) {
            context.showSnackBar('Failed to verify code. Please try again.');
          }
        } finally {
          commonCubit.hideLoading();
        }
      },
    );
  }
}

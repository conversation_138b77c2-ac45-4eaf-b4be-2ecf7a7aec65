import 'dart:async';

import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_modular/flutter_modular.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:koc_app/src/base/base_page_state.dart';
import 'package:koc_app/src/modules/authentication/cubit/authentication_cubit.dart';
import 'package:koc_app/src/modules/authentication/cubit/authentication_state.dart';
import 'package:koc_app/src/modules/authentication/presentation/page/facebook_verification_code_page.dart';
import 'package:koc_app/src/modules/shared/cubit/otp_timer_cubit.dart';
import 'package:koc_app/src/shared/constants/color_constants.dart';
import 'package:koc_app/src/shared/extensions.dart';
import 'package:koc_app/src/shared/mixin/common_mixin.dart';
import 'package:koc_app/src/shared/widgets/common_elevated_button.dart';

class FacebookEmailVerificationPage extends StatefulWidget {
  const FacebookEmailVerificationPage({super.key});

  @override
  State<FacebookEmailVerificationPage> createState() => _FacebookEmailVerificationPageState();
}

class _FacebookEmailVerificationPageState extends BasePageState<FacebookEmailVerificationPage, AuthenticationCubit>
    with CommonMixin {
  final TextEditingController _emailController = TextEditingController();
  final FocusNode _emailFocusNode = FocusNode();
  bool _isLoading = false;
  bool _showEmailError = false;
  Timer? _debounceTimer;

  @override
  void initState() {
    super.initState();
    _emailFocusNode.addListener(_onFocusChange);
  }

  @override
  void dispose() {
    _emailController.dispose();
    _emailFocusNode.removeListener(_onFocusChange);
    _emailFocusNode.dispose();
    _debounceTimer?.cancel();
    super.dispose();
  }

  void _onFocusChange() {
    setState(() {});
  }

  @override
  Widget buildPage(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        backgroundColor: Colors.transparent,
        elevation: 0,
        leading: IconButton(
          icon: const Icon(Icons.arrow_back, color: Colors.black),
          onPressed: () => Navigator.of(context).pop(),
        ),
      ),
      body: _buildBody(),
      bottomSheet: _buildBottomButtons(),
    );
  }

  Widget _buildBody() {
    return Padding(
      padding: EdgeInsets.symmetric(horizontal: 24.r),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          SizedBox(height: 20.r),
          _buildTitle(),
          SizedBox(height: 40.r),
          _buildLogoSection(),
          SizedBox(height: 24.r),
          _buildEmailMessage(),
          SizedBox(height: 24.r),
          _buildEmailField(),
        ],
      ),
    );
  }

  Widget _buildTitle() {
    return Align(
      alignment: Alignment.centerLeft,
      child: Text(
        'Continue with Facebook',
        style: context.textBodyLarge(fontWeight: FontWeight.w700),
        textAlign: TextAlign.left,
      ),
    );
  }

  Widget _buildLogoSection() {
    return Row(
      mainAxisAlignment: MainAxisAlignment.center,
      mainAxisSize: MainAxisSize.min,
      children: [
        SvgPicture.asset(
          'assets/images/ATlogo.svg',
          width: 36.r,
          height: 36.r,
        ),
        SizedBox(width: 12.r),
        Icon(
          Icons.add,
          size: 20.r,
          color: Colors.black,
        ),
        SizedBox(width: 12.r),
        Image.asset(
          'assets/images/socials/facebook.png',
          width: 36.r,
          height: 36.r,
        ),
      ],
    );
  }

  Widget _buildEmailMessage() {
    return Text(
      'Facebook didn\'t share your email address with us. Enter it below so we can verify who you are.',
      style: context.textBodySmall(color: ColorConstants.textColor),
      textAlign: TextAlign.center,
    );
  }

  Widget _buildEmailField() {
    return BlocBuilder<AuthenticationCubit, AuthenticationState>(
      bloc: cubit,
      builder: (context, state) {
        return Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Container(
              height: 36.r,
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(30.r),
                border: Border.all(
                  color: _isEmailError() ? Colors.red : ColorConstants.textButtonColor,
                  width: 1.r,
                ),
              ),
              child: TextField(
                controller: _emailController,
                focusNode: _emailFocusNode,
                keyboardType: TextInputType.emailAddress,
                style: TextStyle(
                  fontSize: 14.r,
                  fontWeight: FontWeight.w400,
                  color: ColorConstants.textColor,
                ),
                decoration: InputDecoration(
                  hintText: 'Email',
                  hintStyle: TextStyle(
                    fontSize: 14.r,
                    fontWeight: FontWeight.w400,
                    color: ColorConstants.hintColor,
                  ),
                  border: InputBorder.none,
                  enabledBorder: InputBorder.none,
                  focusedBorder: InputBorder.none,
                  isDense: true,
                  contentPadding: EdgeInsets.symmetric(horizontal: 16.r, vertical: 8.r),
                ),
                onChanged: (value) {
                  cubit.validateEmail(value);

                  if (_showEmailError) {
                    setState(() {
                      _showEmailError = false;
                    });
                  }

                  _debounceTimer?.cancel();

                  _debounceTimer = Timer(const Duration(milliseconds: 500), () {
                    if (mounted) {
                      setState(() {
                        _showEmailError = true;
                      });
                    }
                  });
                },
                onSubmitted: (value) {
                  cubit.validateEmail(value);
                  setState(() {
                    _showEmailError = true;
                  });
                },
                onEditingComplete: () {
                  cubit.validateEmail(_emailController.text);
                  setState(() {
                    _showEmailError = true;
                  });
                  _emailFocusNode.unfocus();
                },
              ),
            ),
            if (_isEmailError())
              Padding(
                padding: EdgeInsets.only(top: 4.r, left: 16.r),
                child: Text(
                  'Please enter a valid email',
                  style: TextStyle(
                    fontSize: 12.r,
                    fontWeight: FontWeight.w400,
                    color: Colors.red,
                  ),
                ),
              ),
          ],
        );
      },
    );
  }

  Widget _buildBottomButtons() {
    return Container(
      color: Colors.white,
      padding: EdgeInsets.all(16.r),
      child: BlocBuilder<AuthenticationCubit, AuthenticationState>(
        bloc: cubit,
        builder: (context, state) {
          return Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              TextButton(
                onPressed: () {
                  Navigator.of(context).pop();
                },
                style: ButtonStyle(
                  padding: WidgetStateProperty.all(EdgeInsets.symmetric(vertical: 12.r)),
                  alignment: Alignment.centerLeft,
                  splashFactory: NoSplash.splashFactory,
                  overlayColor: WidgetStateProperty.all(Colors.transparent),
                  enableFeedback: false,
                ),
                child: Text(
                  'Cancel',
                  style: TextStyle(
                    fontSize: 14.r,
                    fontWeight: FontWeight.w500,
                    color: ColorConstants.textButtonColor,
                  ),
                ),
              ),
              CommonElevatedButton(
                'Continue',
                state.isValidEmail ? () => _handleContinue() : null,
              )
            ],
          );
        },
      ),
    );
  }

  bool _isEmailError() {
    final email = _emailController.text;
    return _showEmailError && email.isNotEmpty && !cubit.state.isValidEmail;
  }

  Future<void> _handleContinue() async {
    final email = _emailController.text;

    setState(() {
      _isLoading = true;
    });

    try {
      final userExistCheck = await cubit.checkUserExisting(email);

      if (userExistCheck == null || !userExistCheck.isEmailRegistered) {
        setState(() {
          _isLoading = false;
        });
        return;
      }

      final otpTimerCubit = Modular.get<OtpTimerCubit>();
      otpTimerCubit.resetTimerForInitialOtp();

      if (mounted) {
        setState(() {
          _isLoading = false;
        });

        Navigator.of(context).push(
          MaterialPageRoute(
            builder: (context) => FacebookVerificationCodePage(
              email: email,
              isRegistered: userExistCheck.isEmailRegistered,
            ),
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
        context.showSnackBar("Failed to send verification code. Please try again.");
      }
    }
  }
}

import 'dart:convert';
import 'dart:developer' as dev;

import 'package:flutter_modular/flutter_modular.dart';
import 'package:flutter_secure_storage/flutter_secure_storage.dart';
import 'package:koc_app/src/base/cubit/base_cubit.dart';
import 'package:koc_app/src/modules/account/data/model/publisher_sites.dart';
import 'package:koc_app/src/modules/account/data/repository/account_repository.dart';
import 'package:koc_app/src/modules/authentication/cubit/authentication_state.dart';
import 'package:koc_app/src/modules/authentication/data/models/auth_token_info.dart';
import 'package:koc_app/src/modules/authentication/data/models/otp.dart';
import 'package:koc_app/src/modules/authentication/data/models/sign_in_sign_up.dart';
import 'package:koc_app/src/modules/authentication/data/models/user.dart';
import 'package:koc_app/src/modules/authentication/data/repository/authentication_repository.dart';
import 'package:koc_app/src/modules/shared/model/country_selector_item.dart';
import 'package:koc_app/src/modules/shared/repositories/currency_repository.dart';
import 'package:koc_app/src/shared/cache/warm_cache_service.dart';
import 'package:koc_app/src/shared/constants/instance_key.dart';
import 'package:koc_app/src/shared/services/facebook_auth_service.dart';
import 'package:koc_app/src/shared/services/google_auth_service.dart';
import 'package:koc_app/src/shared/validator/validators.dart';

import '../../../shared/services/shared_preferences_service.dart';
import '../../../shared/utils/handle_error.dart';

class AuthenticationCubit extends BaseCubit<AuthenticationState> {
  final AuthenticationRepository _authenticationRepository;
  final AccountRepository _accountRepository;
  final CurrencyRepository _currencyRepository;
  final SharedPreferencesService sharedPreferencesService;
  static const storage = FlutterSecureStorage();

  AuthenticationCubit(
      this._authenticationRepository, this._accountRepository, this._currencyRepository, this.sharedPreferencesService)
      : super(AuthenticationState());

  void selectCountry(CountrySelectorItem country) {
    emit(state.copyWith(country: country));
  }

  Future<UserExistCheck?> checkUserExisting(
    String email, {
    bool updateState = false,
  }) async {
    try {
      showLoading();

      final countryCode = state.country?.country?.code;
      if (countryCode == null) {
        emit(state.copyWith(errorMessage: "Please select country"));
        return null;
      }

      final userExistCheckResponse = await _authenticationRepository.checkUserExistingBy(email, countryCode);
      if (userExistCheckResponse == null) {
        emit(state.copyWith(errorMessage: "Invalid response from server"));
        return null;
      }

      final userExistCheck = UserExistCheck.fromJson(userExistCheckResponse);

      if (updateState) {
        emit(state.copyWith(userExistCheck: userExistCheck));
      }

      await Future.wait([
        sharedPreferencesService.setEmail(email),
        sharedPreferencesService.setToInstance(InstanceConstants.countryCodeKey, countryCode),
        sharedPreferencesService.setToInstance(
            InstanceConstants.isEmailRegisteredKey, userExistCheck.isEmailRegistered),
        sharedPreferencesService.setToInstance(InstanceConstants.isGlobalKey, userExistCheck.isGlobal)
      ]);

      return userExistCheck;
    } catch (e) {
      handleError(e, (message) => emit(state.copyWith(errorMessage: message)));
      return null;
    } finally {
      hideLoading();
    }
  }

  Future<void> handleLoginOrSignup(String email) async {
    final userExistCheck = await checkUserExisting(email, updateState: true);
    if (userExistCheck == null) {
      return;
    }

    final countryCode = state.country?.country?.code;
    if (countryCode == null) {
      emit(state.copyWith(errorMessage: "Country code is not selected"));
      return;
    }

    try {
      String route;

      if (userExistCheck.isEmailRegistered) {
        if (!userExistCheck.users.first.hasPassword) {
          await sendOtpSignIn(email, countryCode);
          route = '/sign-in-by-otp/';
        } else {
          route = '/sign-in-by-password/';
        }
      } else {
        await sendOtpRegister(email, countryCode);
        route = '/sign-up/';
      }

      emit(state.copyWith(route: route, email: email));
    } catch (e) {
      handleError(e, (message) => emit(state.copyWith(errorMessage: message)));
    }
  }

  void toggleObscurePassword() {
    emit(state.copyWith(obscurePassword: !state.obscurePassword));
  }

  void setPasswordValid(bool valid) {
    emit(state.copyWith(isValidPassword: valid));
  }

  Future<void> sendOtpSignIn(String email, String countryCode) async {
    await _authenticationRepository.sendOtpSignIn(email, countryCode);
  }

  Future<void> sendOtpRegister(String email, String countryCode) async {
    SendCodeOtpRequest request = SendCodeOtpRequest(email: email, countryCode: countryCode);
    await _authenticationRepository.sendOtpRegister(request);
  }

  Future<void> verifySignUpOtpCode(String otp) async {
    try {
      final countryCode = await sharedPreferencesService.getCountryCode();
      if (countryCode == null) {
        emit(state.copyWith(errorMessage: "Country code is not selected"));
        return;
      }
      VerifySignUpOtpRequest request = VerifySignUpOtpRequest(email: state.email, countryCode: countryCode, otp: otp);
      await _authenticationRepository.verifySignUpOtpCode(request);
      emit(state.copyWith(route: '/survey/user'));
    } catch (e) {
      handleError(e, (message) => emit(state.copyWith(errorMessage: message)));
    }
  }

  Future<bool> verifySignInOtpCode(String email, String otp) async {
    try {
      final countryCode = await sharedPreferencesService.getCountryCode();
      if (countryCode == null) {
        emit(state.copyWith(errorMessage: "Country code is not selected"));
        return false;
      }
      final result = await _authenticationRepository.verifySignInOtpCode(email, countryCode, otp);
      final authData = AuthTokenInfo.fromJson(result);
      emit(state.copyWith(authTokenInfo: authData));
      await Future.wait([commonCubit.saveToken(authData), getSites(), getCurrency()]);
      return true;
    } catch (e) {
      handleError(e, (message) => emit(state.copyWith(errorMessage: message)));
      return false;
    }
  }

  Future<bool> verifyEmailPassword(String email, String password) async {
    try {
      final countryCode = await sharedPreferencesService.getCountryCode();
      if (countryCode == null) {
        emit(state.copyWith(errorMessage: "Country code is not selected"));
        return false;
      }
      final result = await _authenticationRepository.verifyEmailPassword(email, password, countryCode);
      final authData = AuthTokenInfo.fromJson(result);
      emit(state.copyWith(authTokenInfo: authData));
      await Future.wait([commonCubit.saveToken(authData), getSites(), getCurrency()]);
      return true;
    } catch (e) {
      handleError(e, (message) => emit(state.copyWith(errorMessage: message)));
      return false;
    }
  }

  Future<void> getSites() async {
    try {
      final result = await _accountRepository.getSites();
      List<PublisherSite> sites = (result as List).map((item) => PublisherSite.fromJson(item)).toList();
      if (sites.isNotEmpty) {
        await sharedPreferencesService.setCurrentSiteId(sites.first.id);
        await sharedPreferencesService.setSites(sites);

        WarmCacheService().warmCacheAfterAuth().catchError((e) {
          dev.log('Cache warming after auth failed: $e');
        });
      }
    } catch (e) {
      handleError(e, (message) => emit(state.copyWith(errorMessage: message)));
    }
  }

  Future<void> getCurrency() async {
    try {
      final result = await _currencyRepository.getCurrency();
      await sharedPreferencesService.setPublisherCurrency(result);
    } catch (e) {
      handleError(e, (message) => emit(state.copyWith(errorMessage: message)));
    }
  }

  void validateEmail(String email) {
    emit(state.copyWith(
      isValidEmail: Validators.isValidEmail(email),
    ));
  }

  Future<bool> handleGoogleSignIn(GoogleSignInTokens tokens) async {
    try {
      showLoading();

      if (tokens.accessToken == null) {
        emit(state.copyWith(errorMessage: "Google sign-in failed. Please try again."));
        return false;
      }

      final userExistCheck = await checkUserExisting(tokens.email, updateState: true);
      if (userExistCheck == null) {
        return false;
      }

      final countryCode = state.country?.country?.code;
      if (countryCode == null) {
        emit(state.copyWith(errorMessage: "Country code is not selected"));
        return false;
      }

      final request = SocialSignInPayload(
          accessToken: tokens.accessToken!,
          countryCode: countryCode,
          socialNetworkType: SocialNetworkType.google.value);

      if (userExistCheck.isEmailRegistered) {
        final result = await _authenticationRepository.socialLogin(request);
        final authData = AuthTokenInfo.fromJson(result);
        emit(state.copyWith(authTokenInfo: authData));
        await Future.wait([commonCubit.saveToken(authData), getSites(), getCurrency()]);
      } else {
        if (tokens.displayName != null) {
          final nameParts = tokens.displayName!.split(' ');
          final firstName = nameParts.isNotEmpty ? nameParts.first : '';
          final lastName = nameParts.length > 1 ? nameParts.sublist(1).join(' ') : '';

          Future.wait([
            sharedPreferencesService.setToInstance(InstanceConstants.firstNameKey, firstName),
            sharedPreferencesService.setToInstance(InstanceConstants.lastNameKey, lastName),
          ]);

          if (tokens.photoUrl != null) {
            await sharedPreferencesService.setToInstance(InstanceConstants.profilePictureUrlKey, tokens.photoUrl);
          }
        }
        emit(state.copyWith(route: '/survey/user'));
      }
      return true;
    } catch (e) {
      emit(state.copyWith(errorMessage: 'Login failed. Please try again.'));
      return false;
    } finally {
      hideLoading();
    }
  }

  Future<GoogleSignInTokens?> getGoogleSignInTokensFromStorage() async {
    try {
      final googleAuthData = await storage.read(key: InstanceConstants.googleAuthDataKey);
      if (googleAuthData != null) {
        final data = jsonDecode(googleAuthData);
        return GoogleSignInTokens(
          accessToken: data['accessToken'],
          idToken: data['idToken'],
          email: data['email'],
          displayName: data['displayName'],
          photoUrl: data['photoUrl'],
        );
      } else {
        return null;
      }
    } catch (e) {
      emit(state.copyWith(errorMessage: 'Failed to retrieve Google sign-in tokens.'));
      return null;
    }
  }

  Future<bool> processFacebookLogin(String accessToken) async {
    try {
      showLoading();
      final countryCode = await sharedPreferencesService.getCountryCode();
      if (countryCode == null) {
        emit(state.copyWith(errorMessage: "Country code is not selected"));
        return false;
      }

      final facebookAuthService = Modular.get<FacebookAuthService>();
      final success = await facebookAuthService.processFacebookLogin(
          accessToken, countryCode, (errorMessage) => emit(state.copyWith(errorMessage: errorMessage)));

      if (success) {
        final result = await _authenticationRepository.socialLogin(SocialSignInPayload(
            accessToken: accessToken, countryCode: countryCode, socialNetworkType: SocialNetworkType.facebook.value));
        final authData = AuthTokenInfo.fromJson(result);
        emit(state.copyWith(authTokenInfo: authData));
        await Future.wait([commonCubit.saveToken(authData), getSites(), getCurrency()]);
      }

      return success;
    } catch (e) {
      handleError(e, (message) => emit(state.copyWith(errorMessage: message)));
      return false;
    } finally {
      hideLoading();
    }
  }

  Future<bool> handleFacebookVerifyOtp(String otp) async {
    try {
      showLoading();
      final email = await sharedPreferencesService.getEmail();
      final countryCode = await sharedPreferencesService.getCountryCode();
      final isRegistered = await sharedPreferencesService.getBoolFromInstance(InstanceConstants.isEmailRegisteredKey);

      if (email == null || email.isEmpty || countryCode == null) {
        emit(state.copyWith(errorMessage: "Email or country code is not set"));
        return false;
      }

      final facebookAuthService = Modular.get<FacebookAuthService>();
      final success = await facebookAuthService.handleFacebookVerifyOtp(
          otp, email, countryCode, isRegistered!, (errorMessage) => emit(state.copyWith(errorMessage: errorMessage)));

      if (success) {
        if (isRegistered) {
          final facebookAuthData = await storage.read(key: InstanceConstants.facebookAuthDataKey);
          if (facebookAuthData != null) {
            final data = jsonDecode(facebookAuthData);
            final accessToken = data['accessToken'];

            if (accessToken != null) {
              return await processFacebookLogin(accessToken);
            }
          }
        } else {
          emit(state.copyWith(route: '/survey/user'));
        }
      }

      return success;
    } catch (e) {
      handleError(e, (message) => emit(state.copyWith(errorMessage: message)));
      return false;
    } finally {
      hideLoading();
    }
  }

  Future<FacebookAuthResponse?> getFacebookAuthResponseFromStorage() async {
    try {
      final facebookAuthService = Modular.get<FacebookAuthService>();
      final response = await facebookAuthService.getFacebookAuthResponseFromStorage();

      if (response == null) {
        emit(state.copyWith(errorMessage: 'Failed to retrieve Facebook data.'));
      }

      return response;
    } catch (e) {
      emit(state.copyWith(errorMessage: 'Failed to retrieve Facebook data.'));
      return null;
    }
  }

  Future<void> clearSocialLoginData() async {
    try {
      final googleAuthService = Modular.get<GoogleAuthService>();
      await googleAuthService.signOut();

      final facebookAuthService = Modular.get<FacebookAuthService>();
      await facebookAuthService.signOut();

      await sharedPreferencesService.deleteKey(InstanceConstants.facebookIdKey);
      await sharedPreferencesService.deleteKey(InstanceConstants.firstNameKey);
      await sharedPreferencesService.deleteKey(InstanceConstants.lastNameKey);
      await sharedPreferencesService.deleteKey(InstanceConstants.profilePictureUrlKey);

      emit(state.copyWith(
        oAuth2AccessToken: '',
        accessToken: '',
        refreshToken: '',
      ));
    } catch (e) {
      dev.log('Error clearing social login data: $e');
    }
  }

  Future<FacebookSignInResult> handleFacebookSignIn() async {
    try {
      showLoading();
      final facebookAuthService = Modular.get<FacebookAuthService>();
      final facebookResponse = await facebookAuthService.signInWithFacebook();

      if (facebookResponse == null) {
        return const FacebookSignInResult(success: false, cancelled: true);
      }

      if (facebookResponse.email != null && facebookResponse.email!.isNotEmpty) {
        final userExistCheck = await checkUserExisting(
          facebookResponse.email!,
          updateState: true,
        );

        if (userExistCheck == null) {
          return FacebookSignInResult(
            success: false,
            errorMessage: state.errorMessage.isNotEmpty ? state.errorMessage : 'Failed to verify user information.',
          );
        }

        if (userExistCheck.isEmailRegistered) {
          if (facebookResponse.accessToken != null) {
            final loginResult = await processFacebookLogin(facebookResponse.accessToken!);
            if (loginResult) {
              return const FacebookSignInResult(success: true, navigateTo: "/navigation");
            } else {
              return FacebookSignInResult(
                  success: false,
                  errorMessage:
                      state.errorMessage.isNotEmpty ? state.errorMessage : 'Facebook login failed. Please try again.');
            }
          } else {
            return const FacebookSignInResult(success: false, errorMessage: 'Facebook login failed. Please try again.');
          }
        } else {
          if (facebookResponse.accessToken != null) {
            if (facebookResponse.first_name != null && facebookResponse.last_name != null) {
              await Future.wait([
                sharedPreferencesService.setToInstance(InstanceConstants.firstNameKey, facebookResponse.first_name!),
                sharedPreferencesService.setToInstance(InstanceConstants.lastNameKey, facebookResponse.last_name!),
                sharedPreferencesService.setToInstance(InstanceConstants.facebookIdKey, facebookResponse.id),
              ]);

              if (facebookResponse.picture != null && facebookResponse.picture!.data.url.isNotEmpty) {
                await sharedPreferencesService.setToInstance(
                    InstanceConstants.profilePictureUrlKey, facebookResponse.picture!.data.url);
              }
            }
            return const FacebookSignInResult(success: true, navigateTo: '/survey/user');
          } else {
            return const FacebookSignInResult(success: false, errorMessage: 'Facebook login failed. Please try again.');
          }
        }
      } else {
        return const FacebookSignInResult(success: true, navigateTo: '/facebook-email-verification/');
      }
    } catch (e) {
      handleError(e, (message) => emit(state.copyWith(errorMessage: message)));
      return const FacebookSignInResult(
          success: false, errorMessage: 'Failed to sign in with Facebook. Please try again.');
    } finally {
      hideLoading();
    }
  }
}

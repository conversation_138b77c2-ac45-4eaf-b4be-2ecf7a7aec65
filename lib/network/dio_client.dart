import 'package:dio/dio.dart';
import 'package:flutter_dotenv/flutter_dotenv.dart';
import 'package:koc_app/network/token_interceptor.dart';
import 'package:koc_app/src/shared/cache/cache.dart';

import 'dio_interceptor.dart';

class DioClient {
  late Dio _dio;
  static final String? baseUrl = dotenv.env['BASE_URL'];
  static final String? _clientId = dotenv.env['CLIENT_ID'];
  late CacheService _cacheService;

  DioClient() {
    _dio = Dio(BaseOptions(
      baseUrl: baseUrl!,
      connectTimeout: const Duration(seconds: 30),
      receiveTimeout: const Duration(seconds: 30),
      headers: {
        "Content-Type": "application/json",
        'X-Accesstrade-User-Type': 'publisher',
        'accept': 'application/json',
        "clientId": _clientId
      },
    ));

    _dio.interceptors.add(TokenInterceptor(_dio));
    _dio.interceptors.add(DioInterceptor());

    _cacheService = CacheService(dio: _dio);
    _initializeCache();
  }

  Future<void> _initializeCache() async {
    final maxApiCacheSize = int.tryParse(dotenv.env['MAX_API_CACHE_SIZE'] ?? '50') ?? 50;
    final maxImageCacheSize = int.tryParse(dotenv.env['MAX_IMAGE_CACHE_SIZE'] ?? '100') ?? 100;
    final maxTotalCacheSize = int.tryParse(dotenv.env['MAX_TOTAL_CACHE_SIZE'] ?? '200') ?? 200;

    final cacheConfig = CacheConfig(
      defaultTtl: 3600,
      enableCaching: true,
      enableImageCaching: true,

      maxApiCacheSize: maxApiCacheSize * 1024 * 1024,
      maxImageCacheSize: maxImageCacheSize * 1024 * 1024,
      maxTotalCacheSize: maxTotalCacheSize * 1024 * 1024,

      defaultImageTtl: 7 * 24 * 3600, // 7 days for images
      cleanupStrategy: CacheCleanupStrategy.lru,

      /// Endpoint-specific TTLs based on data volatility
      endpointTtls: {
        /// User profile data - cache for 1 day (86400s)
        '/v3/publishers/me/account': 86400,
        '/v3/publishers/me/avatar': 86400,
        '/v3/publishers/me/currency': 86400,
        '/v3/publishers/me/account/sso-key': 86400,

        /// Publisher sites and banks - cache for 6 hours (21600s)
        '/v3/publishers/me/sites': 21600,
        '/v3/publishers/banks': 21600,
        '/v3/publishers/countries/minimum-payment-details': 21600,

        /// Campaign data - cache for 30 minutes (1800s)
        '/v3/publishers/me/sites/*/campaigns/featured-summary': 1800,
        '/v3/publishers/me/sites/*/campaigns/top-summary': 1800,
        '/v3/publishers/me/sites/*/campaigns/fastest-growing-summary': 1800,
        '/v3/publishers/me/sites/*/campaigns/*/creatives': 1800,
        '/v3/publishers/me/sites/*/campaigns/*/custom-creatives': 1800,

        /// Reports and statistics - cache for 15 minutes (900s)
        '/v3/publishers/me/reports/summary': 900,
        '/v3/publishers/me/reports/monthly': 900,
        '/v3/publishers/me/reports/campaign/chart': 900,
        '/v3/publishers/me/payment-summary': 900,
        '/v3/publishers/me/payment/invoice-detail/*': 900,
        '/v3/publishers/me/payment/process-stage-details': 900,

        /// Notifications - cache for 5 minutes (300s)
        '/v3/publishers/me/notifications': 300,
        '/v3/publishers/me/notifications/*': 300,

        /// Vouchers - cache for 30 minutes (1800s)
        '/v3/publishers/me/vouchers': 1800,
        '/v3/publishers/me/vouchers/*': 1800,
        '/v3/publishers/me/voucher-categories': 1800,

        /// Categories - cache for 6 hours (21600s)
        '/v3/publishers/me/sites/*/categories': 21600,
      },
      excludedEndpoints: [
        /// Exclude payment and sensitive endpoints
        '/v3/users/me/password',
        '/v3/publishers/me/bankaccount',
        '/v3/publishers/me/otp',
        '/v3/publishers/me/account/phone',
        '/v3/publishers/me/account/address',
        '/v3/publishers/me/account/name',

        /// Exclude data modification endpoints
        '/v3/publishers/me/sites/*/campaigns/*/creatives/custom',
        '/v3/publishers/me/sites/*/campaigns/*/creatives/multiple-custom',
        '/v3/publishers/me/reports/conversion/async-export',
      ],
      cacheOnErrorEndpoints: [
        /// Critical data that should be served from cache on error
        '/v3/publishers/me/sites',
        '/v3/publishers/me/account',
        '/v3/publishers/me/currency',
        '/v3/publishers/banks',
        '/v3/publishers/me/sites/*/campaigns/featured-summary',
        '/v3/publishers/me/sites/*/campaigns/top-summary',
        '/v3/publishers/me/sites/*/campaigns/fastest-growing-summary',
        '/v3/publishers/me/sites/*/categories',
        '/v3/publishers/me/voucher-categories',
      ],
      serveStaleDataOnError: true,
      cachePostRequests: false,
    );

    await _cacheService.init();
    await _cacheService.updateConfig(cacheConfig);
  }

  Dio get dio => _dio;

  CacheService get cacheService => _cacheService;
}

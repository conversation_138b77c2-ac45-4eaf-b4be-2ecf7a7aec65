# 🎨 Splash Screen Management Guide

Hướng dẫn quản lý splash screen cho KOC-App sử dụng custom script thay vì flutter_native_splash package.

## 🚀 Quick Start

### Tạo splash screen mới với kích thước mặc định:
```bash
./scripts/generate_splash.sh
```

### Tùy chỉnh kích thước:
```bash
# Cú pháp: ./scripts/generate_splash.sh [logo_path] [android_size] [ios_size] [background_color]
./scripts/generate_splash.sh assets/images/ATlogo-white-ios-120.png 180 120 "#FFB522"
```

## 📱 Kích thước được tạo

### Android
- **mdpi (1x)**: 45x45px
- **hdpi (1.5x)**: 67x67px  
- **xhdpi (2x)**: 90x90px
- **xxhdpi (3x)**: 135x135px
- **xxxhdpi (4x)**: 180x180px (base size)
- **Android 12+**: 960x960px

### iOS
- **@1x**: 120x120px (base size)
- **@2x**: 240x240px
- **@3x**: 360x360px

## 🛠️ Scripts có sẵn

| Script | Mô tả |
|--------|-------|
| `./scripts/generate_splash.sh` | Tạo splash screen mới |
| `./scripts/clean_splash.sh` | Xóa tất cả splash screen cũ |
| `./scripts/demo_splash.sh` | Demo với nhiều cấu hình khác nhau |

## ⚙️ Cấu hình

Chỉnh sửa `scripts/splash_config.json` để thay đổi cấu hình mặc định:

```json
{
  "background_color": "#FFB522",
  "android": {
    "base_size": 180,
    "android12_size": 960
  },
  "ios": {
    "size": 120
  }
}
```

## 📝 Ví dụ sử dụng

### Thay đổi kích thước Android thành 200px:
```bash
./scripts/generate_splash.sh assets/images/ATlogo-white-ios-120.png 200 120 "#FFB522"
```

### Thay đổi màu nền thành xanh dương:
```bash
./scripts/generate_splash.sh assets/images/ATlogo-white-ios-120.png 180 120 "#2196F3"
```

### Tạo splash nhỏ hơn cho thiết bị cũ:
```bash
./scripts/generate_splash.sh assets/images/ATlogo-white-ios-120.png 120 80 "#FFB522"
```

## 🔄 Workflow

1. **Chuẩn bị logo**: Đảm bảo có file logo PNG chất lượng cao
2. **Chạy script**: `./scripts/generate_splash.sh [options]`
3. **Clean & rebuild**: `flutter clean && flutter pub get`
4. **Test**: Chạy app trên thiết bị thật để kiểm tra

## ✅ Ưu điểm so với flutter_native_splash

- ✅ **Kiểm soát hoàn toàn** kích thước và chất lượng
- ✅ **Không phụ thuộc** package bên ngoài
- ✅ **Tùy chỉnh dễ dàng** cho từng project
- ✅ **Tự động hóa** toàn bộ quy trình
- ✅ **Hỗ trợ cả Android và iOS**
- ✅ **Responsive** với tất cả density màn hình

## 🐛 Troubleshooting

### Lỗi "Python 3 not found":
```bash
# macOS
brew install python3

# Ubuntu/Debian
sudo apt install python3 python3-pip
```

### Lỗi "Pillow not found":
```bash
pip3 install Pillow
```

### Lỗi permission denied:
```bash
chmod +x scripts/*.sh
```

### Logo không hiển thị đúng:
- Kiểm tra file logo có tồn tại không
- Đảm bảo logo có background trong suốt
- Thử với kích thước khác

## 📚 Chi tiết kỹ thuật

### File được tạo:

**Android:**
- `android/app/src/main/res/drawable-*/splash.png`
- `android/app/src/main/res/drawable-*/android12splash.png`

**iOS:**
- `ios/Runner/Assets.xcassets/LaunchImage.imageset/LaunchImage.png`
- `ios/Runner/Assets.xcassets/LaunchImage.imageset/<EMAIL>`
- `ios/Runner/Assets.xcassets/LaunchImage.imageset/<EMAIL>`
- `ios/Runner/Assets.xcassets/LaunchImage.imageset/Contents.json`

### File cấu hình được cập nhật:
- `android/app/src/main/res/drawable/launch_background.xml`
- `android/app/src/main/res/drawable-v21/launch_background.xml`
- `android/app/src/main/res/values*/styles.xml`

---

💡 **Tip**: Luôn test splash screen trên thiết bị thật vì emulator có thể hiển thị khác so với thiết bị thực.
